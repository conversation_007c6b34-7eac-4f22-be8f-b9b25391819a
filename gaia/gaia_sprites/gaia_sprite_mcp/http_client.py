#!/usr/bin/env python3
"""
HTTP Client for Sprite MCP Server

Provides utilities to connect to the Sprite MCP server via HTTP streaming protocol.
This is useful for web applications and remote connections.
"""

import asyncio
import json
import httpx
from typing import Dict, List, Any, Optional

class SpriteMCPHttpClient:
    """HTTP client for connecting to Sprite MCP server"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.tools_url = f"{self.base_url}/tools"
        self.health_url = f"{self.base_url}/health"
        self.client = None
    
    async def __aenter__(self):
        self.client = httpx.AsyncClient()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """List available tools from the HTTP server"""
        if not self.client:
            raise RuntimeError("Client not initialized. Use 'async with' context manager.")

        try:
            response = await self.client.get(self.tools_url)
            response.raise_for_status()

            result = response.json()
            if "tools" in result:
                return result["tools"]
            else:
                return []

        except Exception as e:
            print(f"Error listing tools: {e}")
            return []
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a specific tool with arguments"""
        if not self.client:
            raise RuntimeError("Client not initialized. Use 'async with' context manager.")

        try:
            tool_url = f"{self.tools_url}/{name}"
            response = await self.client.post(
                tool_url,
                json=arguments,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            result = response.json()
            return result

        except Exception as e:
            return {"error": f"Error calling tool {name}: {str(e)}"}
    
    async def health_check(self) -> bool:
        """Check if the HTTP server is healthy and responding"""
        try:
            if not self.client:
                return False

            response = await self.client.get(self.health_url)
            response.raise_for_status()

            health_data = response.json()
            return health_data.get("status") == "healthy"
        except:
            return False

async def test_http_client():
    """Test the HTTP client connection"""
    print("🧪 Testing Sprite MCP HTTP Client")
    print("=" * 40)
    
    async with SpriteMCPHttpClient() as client:
        # Health check
        print("Checking server health...")
        healthy = await client.health_check()
        if not healthy:
            print("✗ Server not responding or no tools available")
            print("Make sure the server is running with:")
            print("  python3 sprite_mcp_server.py --transport http")
            return False
        
        print("✓ Server is healthy")
        
        # List tools
        print("\nListing available tools...")
        tools = await client.list_tools()
        print(f"✓ Found {len(tools)} tools:")
        for tool in tools[:5]:  # Show first 5
            print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')[:60]}...")
        
        if len(tools) > 5:
            print(f"  ... and {len(tools) - 5} more tools")
        
        # Test a simple tool call
        if tools:
            print(f"\nTesting tool call...")
            try:
                # Try to call a simple tool (agsearch)
                result = await client.call_tool("agsearch", {"query": "test", "limit": 1})
                if "error" not in result:
                    print("✓ Tool call successful")
                else:
                    print(f"✗ Tool call failed: {result['error']}")
            except Exception as e:
                print(f"✗ Tool call exception: {e}")
        
        return True

def create_sprite_http_tools():
    """Create a list of HTTP-based sprite tools for use with agents"""
    
    class HttpSpriteTool:
        def __init__(self, name: str, description: str, client: SpriteMCPHttpClient):
            self.name = name
            self.description = description
            self.client = client
        
        async def __call__(self, **kwargs):
            return await self.client.call_tool(self.name, kwargs)
    
    async def get_tools():
        async with SpriteMCPHttpClient() as client:
            tools_info = await client.list_tools()
            tools = []
            for tool_info in tools_info:
                tool = HttpSpriteTool(
                    name=tool_info.get("name", "unknown"),
                    description=tool_info.get("description", "No description"),
                    client=client
                )
                tools.append(tool)
            return tools
    
    return asyncio.run(get_tools())

# Usage examples
USAGE_EXAMPLES = """
# HTTP Client Usage Examples

## Basic Usage
```python
import asyncio
from gaia_sprite_mcp.http_client import SpriteMCPHttpClient

async def main():
    async with SpriteMCPHttpClient() as client:
        # List available tools
        tools = await client.list_tools()
        print(f"Available tools: {len(tools)}")
        
        # Call a tool
        result = await client.call_tool("agsearch", {"query": "Tesla", "limit": 5})
        print(result)

asyncio.run(main())
```

## Health Check
```python
async def check_server():
    async with SpriteMCPHttpClient("http://localhost:8000") as client:
        if await client.health_check():
            print("Server is running!")
        else:
            print("Server is not responding")
```

## Custom Server URL
```python
client = SpriteMCPHttpClient("http://your-server.com:8000")
```
"""

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Sprite MCP HTTP Client")
    parser.add_argument("--test", action="store_true", help="Run client tests")
    parser.add_argument("--url", default="http://localhost:8000", help="Server URL")
    parser.add_argument("--examples", action="store_true", help="Show usage examples")
    
    args = parser.parse_args()
    
    if args.examples:
        print(USAGE_EXAMPLES)
    elif args.test:
        # Update client URL if provided
        if args.url != "http://localhost:8000":
            print(f"Testing with custom URL: {args.url}")
        
        success = asyncio.run(test_http_client())
        exit(0 if success else 1)
    else:
        print("Sprite MCP HTTP Client")
        print("Usage:")
        print("  python3 http_client.py --test")
        print("  python3 http_client.py --examples")
        print("  python3 http_client.py --help")
