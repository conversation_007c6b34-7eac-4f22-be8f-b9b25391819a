#!/usr/bin/env python3
"""
Sprite MCP Server Base Class

Base class for Gaia Sprite MCP servers with HTTP streaming protocol support.
Provides common functionality for different server levels.
"""

import sys
import os
import json
import argparse
import asyncio
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

# Add parent directory to path for sprite imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import FastMCP
from mcp.server.fastmcp import FastMCP

class SpriteMCPServerBase(ABC):
    """Base class for Sprite MCP servers"""
    
    def __init__(self, server_name: str = "sprite-mcp-server"):
        self.server_name = server_name
        self.mcp = FastMCP(server_name)
        self.sprites_available = False
        self.tool_registry = {}
        
        # Initialize sprites and tools
        self._init_sprites()
        self._register_tools()
    
    @abstractmethod
    def _init_sprites(self):
        """Initialize sprite imports - implemented by subclasses"""
        pass
    
    @abstractmethod
    def _register_tools(self):
        """Register MCP tools - implemented by subclasses"""
        pass
    
    def _safe_import_sprites(self):
        """Safely import all basic sprites"""
        try:
            from spr_agf_agsearch import quick_search
            from spr_tamarind import market_research
            from spr_ext_search import web_search, news_search
            from spr_agf_omnisearch import omni_search
            from spr_ext_wiki import wiki_search
            from spr_agf_multimodel import multimodel_query
            from spr_agf_frames import frame_search, frame_list
            from spr_agf_investors import investor_search, investor_stats
            from spr_llm_smart import (
                llm_completion_text, llm_completion_json, llm_analyze_code,
                llm_generate_code, llm_explain_concept, llm_debug_error
            )
            from spr_ext_deep_research import (
                deep_research_simple, deep_research_robust, deep_research_batch,
                deep_research_load, deep_research_list
            )

            return {
                'quick_search': quick_search,
                'market_research': market_research,
                'web_search': web_search,
                'news_search': news_search,
                'omni_search': omni_search,
                'wiki_search': wiki_search,
                'multimodel_query': multimodel_query,
                'frame_search': frame_search,
                'frame_list': frame_list,
                'investor_search': investor_search,
                'investor_stats': investor_stats,
                'llm_completion_text': llm_completion_text,
                'llm_completion_json': llm_completion_json,
                'llm_analyze_code': llm_analyze_code,
                'llm_generate_code': llm_generate_code,
                'llm_explain_concept': llm_explain_concept,
                'llm_debug_error': llm_debug_error,
                'deep_research_simple': deep_research_simple,
                'deep_research_robust': deep_research_robust,
                'deep_research_batch': deep_research_batch,
                'deep_research_load': deep_research_load,
                'deep_research_list': deep_research_list
            }
        except ImportError as e:
            print(f"Warning: Could not import sprites: {e}", file=sys.stderr)
            return {}
    
    def _register_basic_tools(self, sprites):
        """Register basic sprite tools"""
        if not sprites:
            return
        
        @self.mcp.tool()
        def agsearch(query: str, limit: int = 100) -> dict:
            """Search AgFunder company database"""
            try:
                results, stats = sprites['quick_search'](query, limit=limit)
                return {"tool": "agsearch", "query": query, "limit": limit, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "agsearch"}
        
        @self.mcp.tool()
        def market_research_tool(query: str = "AgTech") -> dict:
            """Get market research data (TAM/CAGR)"""
            try:
                results, stats = sprites['market_research'](query)
                return {"tool": "market_research", "query": query, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "market_research"}
        
        @self.mcp.tool()
        def web_search_tool(query: str, sources: list = None, num_results: int = 10) -> dict:
            """Multi-source web search (web, news, scholar, patents)"""
            try:
                results, stats = sprites['web_search'](query=query, sources=sources, num_results=num_results)
                return {"tool": "web_search", "query": query, "sources": sources, "num_results": num_results, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "web_search"}
        
        @self.mcp.tool()
        def news_search_tool(query: str, num_results: int = 10) -> dict:
            """Specialized news search"""
            try:
                results, stats = sprites['news_search'](query=query, num_results=num_results)
                return {"tool": "news_search", "query": query, "num_results": num_results, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "news_search"}
        
        @self.mcp.tool()
        def omni_search_tool(query: str, limit: int = 100) -> dict:
            """Internal omni database search"""
            try:
                results, stats = sprites['omni_search'](query, limit=limit)
                return {"tool": "omni_search", "query": query, "limit": limit, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "omni_search"}
        
        @self.mcp.tool()
        def wiki_search_tool(query: str, sentences: int = 3) -> dict:
            """Wikipedia search and summaries"""
            try:
                results, stats = sprites['wiki_search'](query, sentences=sentences)
                return {"tool": "wiki_search", "query": query, "sentences": sentences, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "wiki_search"}
        
        @self.mcp.tool()
        def multimodel_query_tool(prompt: str, models: list = None, parallel: bool = True) -> dict:
            """Query multiple LLM models and get aggregated responses"""
            try:
                result = sprites['multimodel_query'](prompt=prompt, models=models, parallel=parallel)
                return {"tool": "multimodel_query", "prompt": prompt, "models": result.get("models", []), "parallel": parallel, "results": result.get("results", {}), "operational": result.get("operational", {})}
            except Exception as e:
                return {"error": str(e), "tool": "multimodel_query"}
        
        @self.mcp.tool()
        def frame_search_tool(query: str, section_slug: str = "agbase", frame_slug: str = "agbase_org__slim", limit: int = 100) -> dict:
            """Search data frames with text query"""
            try:
                results, stats = sprites['frame_search'](query=query, section_slug=section_slug, frame_slug=frame_slug, limit=limit)
                return {"tool": "frame_search", "query": query, "section_slug": section_slug, "frame_slug": frame_slug, "limit": limit, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "frame_search"}
        
        @self.mcp.tool()
        def frame_list_tool(root_dir: str = "/var/lib/gaia/GAIA_FS/frames/") -> dict:
            """List available parquet data frames"""
            try:
                frames, stats = sprites['frame_list'](root_dir=root_dir)
                return {"tool": "frame_list", "root_dir": root_dir, "frames": frames, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "frame_list"}
        
        @self.mcp.tool()
        def investor_search_tool(name: str = "", country: str = "", investor_type: str = "venture vc", min_investments: int = 5, limit: int = 100) -> dict:
            """Search for investors with filters"""
            try:
                results, stats = sprites['investor_search'](name=name, country=country, investor_type=investor_type, min_investments=min_investments, limit=limit)
                return {"tool": "investor_search", "name": name, "country": country, "investor_type": investor_type, "min_investments": min_investments, "limit": limit, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "investor_search"}
        
        @self.mcp.tool()
        def investor_stats_tool(investor_name: str, country: str = "US") -> dict:
            """Get detailed investor statistics and portfolio analysis"""
            try:
                stats, info = sprites['investor_stats'](investor_name=investor_name, country=country)
                return {"tool": "investor_stats", "investor_name": investor_name, "country": country, "stats": stats, "info": info}
            except Exception as e:
                return {"error": str(e), "tool": "investor_stats"}

        @self.mcp.tool()
        def llm_text_completion(prompt: str, system_prompt: str = None, model: str = None) -> dict:
            """Generate text completion using LLM"""
            try:
                result = sprites['llm_completion_text'](prompt=prompt, system_prompt=system_prompt, model=model)
                return {"tool": "llm_text_completion", "prompt": prompt, "system_prompt": system_prompt, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_text_completion"}

        @self.mcp.tool()
        def llm_json_completion(prompt: str, system_prompt: str = None, schema: dict = None, model: str = None) -> dict:
            """Generate structured JSON completion using LLM"""
            try:
                result = sprites['llm_completion_json'](prompt=prompt, system_prompt=system_prompt, schema=schema, model=model)
                return {"tool": "llm_json_completion", "prompt": prompt, "system_prompt": system_prompt, "schema": schema, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_json_completion"}

        @self.mcp.tool()
        def llm_code_analysis(code: str, language: str = "python", model: str = None) -> dict:
            """Analyze code quality, issues, and suggestions"""
            try:
                result = sprites['llm_analyze_code'](code=code, language=language, model=model)
                return {"tool": "llm_code_analysis", "code": code, "language": language, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_code_analysis"}

        @self.mcp.tool()
        def llm_code_generation(description: str, language: str = "python", style: str = "clean", model: str = None) -> dict:
            """Generate code based on description"""
            try:
                result = sprites['llm_generate_code'](description=description, language=language, style=style, model=model)
                return {"tool": "llm_code_generation", "description": description, "language": language, "style": style, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_code_generation"}

        @self.mcp.tool()
        def llm_concept_explanation(concept: str, level: str = "intermediate", model: str = None) -> dict:
            """Explain programming or technical concepts"""
            try:
                result = sprites['llm_explain_concept'](concept=concept, level=level, model=model)
                return {"tool": "llm_concept_explanation", "concept": concept, "level": level, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_concept_explanation"}

        @self.mcp.tool()
        def llm_error_debugging(error_message: str, code_context: str = "", model: str = None) -> dict:
            """Debug errors and provide solutions"""
            try:
                result = sprites['llm_debug_error'](error_message=error_message, code_context=code_context, model=model)
                return {"tool": "llm_error_debugging", "error_message": error_message, "code_context": code_context, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_error_debugging"}

        @self.mcp.tool()
        def deep_research_simple_tool(query: str, model: str = "o4-mini-deep-research", save_slug: str = None, project: str = "general") -> dict:
            """Perform simple deep research with web search and code interpretation"""
            try:
                result = sprites['deep_research_simple'](query=query, model=model, save_slug=save_slug, project=project)
                return {"tool": "deep_research_simple", "query": query, "model": model, "save_slug": save_slug, "project": project, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_simple"}

        @self.mcp.tool()
        def deep_research_robust_tool(query: str, criteria: str = "Comprehensive, accurate, well-sourced research", quality_level: str = "medium", execution_mode: str = "parallel") -> dict:
            """Perform robust deep research with multi-critic evaluation"""
            try:
                result = sprites['deep_research_robust'](query=query, criteria=criteria, quality_level=quality_level, execution_mode=execution_mode)
                return {"tool": "deep_research_robust", "query": query, "criteria": criteria, "quality_level": quality_level, "execution_mode": execution_mode, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_robust"}

        @self.mcp.tool()
        def deep_research_batch_tool(queries: dict, model: str = "o4-mini-deep-research", project: str = "batch_research", parallel: bool = True) -> dict:
            """Perform batch deep research on multiple queries"""
            try:
                result = sprites['deep_research_batch'](queries=queries, model=model, project=project, parallel=parallel)
                return {"tool": "deep_research_batch", "queries": queries, "model": model, "project": project, "parallel": parallel, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_batch"}

        @self.mcp.tool()
        def deep_research_load_tool(slug: str, project: str = "general") -> dict:
            """Load previously saved research results"""
            try:
                result = sprites['deep_research_load'](slug=slug, project=project)
                return {"tool": "deep_research_load", "slug": slug, "project": project, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_load"}

        @self.mcp.tool()
        def deep_research_list_tool(project: str = "general") -> dict:
            """List all saved research in a project"""
            try:
                result = sprites['deep_research_list'](project=project)
                return {"tool": "deep_research_list", "project": project, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_list"}
        
        # Update tool registry
        self.tool_registry = {
            "agsearch": {"name": "agsearch", "description": "Search AgFunder database"},
            "market_research_tool": {"name": "market_research_tool", "description": "Get market research data"},
            "web_search_tool": {"name": "web_search_tool", "description": "Multi-source web search"},
            "news_search_tool": {"name": "news_search_tool", "description": "News search"},
            "omni_search_tool": {"name": "omni_search_tool", "description": "Internal omni search"},
            "wiki_search_tool": {"name": "wiki_search_tool", "description": "Wikipedia search"},
            "multimodel_query_tool": {"name": "multimodel_query_tool", "description": "Multi-LLM queries"},
            "frame_search_tool": {"name": "frame_search_tool", "description": "Data frame search"},
            "frame_list_tool": {"name": "frame_list_tool", "description": "List data frames"},
            "investor_search_tool": {"name": "investor_search_tool", "description": "Investor search"},
            "investor_stats_tool": {"name": "investor_stats_tool", "description": "Investor statistics"},
            "llm_text_completion": {"name": "llm_text_completion", "description": "Generate text using LLM"},
            "llm_json_completion": {"name": "llm_json_completion", "description": "Generate structured JSON using LLM"},
            "llm_code_analysis": {"name": "llm_code_analysis", "description": "Analyze code quality and issues"},
            "llm_code_generation": {"name": "llm_code_generation", "description": "Generate code from description"},
            "llm_concept_explanation": {"name": "llm_concept_explanation", "description": "Explain technical concepts"},
            "llm_error_debugging": {"name": "llm_error_debugging", "description": "Debug errors and provide solutions"},
            "deep_research_simple_tool": {"name": "deep_research_simple_tool", "description": "Simple deep research with web search"},
            "deep_research_robust_tool": {"name": "deep_research_robust_tool", "description": "Robust deep research with multi-critic evaluation"},
            "deep_research_batch_tool": {"name": "deep_research_batch_tool", "description": "Batch deep research on multiple queries"},
            "deep_research_load_tool": {"name": "deep_research_load_tool", "description": "Load saved research results"},
            "deep_research_list_tool": {"name": "deep_research_list_tool", "description": "List saved research in project"}
        }
    
    async def run_http_server(self, host: str = "localhost", port: int = 8000):
        """Run the MCP server using HTTP streaming protocol"""
        try:
            import uvicorn
            from fastapi import FastAPI
            from fastapi.middleware.cors import CORSMiddleware
            
            print(f"Starting {self.server_name} on HTTP... (Sprites available: {self.sprites_available})", file=sys.stderr)
            print(f"Server will be available at: http://{host}:{port}", file=sys.stderr)
            
            # Create FastAPI app
            app = FastAPI(title=self.server_name, version="1.0.0")
            
            # Add CORS middleware
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            @app.get("/")
            async def root():
                return {
                    "name": self.server_name,
                    "version": "1.0.0",
                    "sprites_available": self.sprites_available,
                    "tools_count": len(self.tool_registry)
                }
            
            @app.get("/tools")
            async def list_tools():
                """List all available sprite tools"""
                if not self.sprites_available:
                    return {"tools": [], "error": "Sprites not available"}
                return {"tools": list(self.tool_registry.values())}
            
            @app.post("/tools/{tool_name}")
            async def call_tool(tool_name: str, arguments: dict):
                """Call a specific tool with arguments"""
                if not self.sprites_available:
                    return {"error": "Sprites not available"}
                
                if tool_name not in self.tool_registry:
                    return {"error": f"Tool {tool_name} not found"}
                
                try:
                    result = self.mcp.call_tool(tool_name, arguments)
                    return {"result": result, "success": True}
                except Exception as e:
                    return {"error": str(e), "success": False}
            
            @app.get("/health")
            async def health_check():
                """Health check endpoint"""
                return {
                    "status": "healthy",
                    "sprites_available": self.sprites_available,
                    "tools_count": len(self.tool_registry)
                }
            
            # Run with uvicorn
            config = uvicorn.Config(app=app, host=host, port=port, log_level="info")
            server_instance = uvicorn.Server(config)
            await server_instance.serve()
            
        except ImportError as e:
            print(f"HTTP transport dependencies not available: {e}", file=sys.stderr)
            print("Install with: pip install uvicorn fastapi", file=sys.stderr)
            print("Falling back to stdio transport...", file=sys.stderr)
            self.run_stdio_server()
    
    def run_stdio_server(self):
        """Run the MCP server using stdio protocol (legacy)"""
        print(f"Starting {self.server_name} on stdio... (Sprites available: {self.sprites_available})", file=sys.stderr)
        self.mcp.run()
    
    def run_server(self, transport: str = "http", host: str = "localhost", port: int = 8000):
        """Run the server with specified transport"""
        if transport == "http":
            asyncio.run(self.run_http_server(host, port))
        else:
            self.run_stdio_server()
    
    @classmethod
    def main(cls):
        """Main entry point with command line argument parsing"""
        parser = argparse.ArgumentParser(description=f"{cls.__name__}")
        parser.add_argument("--transport", choices=["stdio", "http"], default="http", 
                           help="Transport protocol to use (default: http)")
        parser.add_argument("--host", default="localhost", 
                           help="Host to bind to for HTTP transport (default: localhost)")
        parser.add_argument("--port", type=int, default=8000, 
                           help="Port to bind to for HTTP transport (default: 8000)")
        
        args = parser.parse_args()
        
        # Create server instance and run
        server = cls()
        server.run_server(args.transport, args.host, args.port)
