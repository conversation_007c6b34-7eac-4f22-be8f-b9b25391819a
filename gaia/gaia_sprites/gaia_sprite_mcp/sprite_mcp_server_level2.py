#!/usr/bin/env python3
"""
Sprite MCP Server Level 2

Level 2 MCP server with basic sprite tools PLUS sprite_codeagent as an MCP tool.
Provides all 11 core Gaia sprites + the AI CodeAgent for complex algorithms.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sprite_mcp_server_base import SpriteMCPServerBase

class SpriteMCPServerLevel2(SpriteMCPServerBase):
    """Level 2 MCP server with basic sprites + CodeAgent"""
    
    def __init__(self):
        super().__init__("sprite-mcp-server-level2")
        self.codeagent_available = False
    
    def _init_sprites(self):
        """Initialize basic sprites + CodeAgent"""
        # Initialize basic sprites
        sprites = self._safe_import_sprites()
        if sprites:
            self.sprites_available = True
            self.sprites = sprites
            print(f"✓ Level 2: Loaded {len(sprites)} basic sprites", file=sys.stderr)
        else:
            self.sprites_available = False
            self.sprites = {}
            print("✗ Level 2: No basic sprites available", file=sys.stderr)
        
        # Try to import CodeAgent
        try:
            # Add parent directory to path for sprite_codeagent import
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            
            from sprite_codeagent import run_sprite_algorithm, create_sprite_codeagent
            self.codeagent_available = True
            self.run_sprite_algorithm = run_sprite_algorithm
            self.create_sprite_codeagent = create_sprite_codeagent
            print("✓ Level 2: CodeAgent available", file=sys.stderr)
        except ImportError as e:
            self.codeagent_available = False
            print(f"✗ Level 2: CodeAgent not available: {e}", file=sys.stderr)
    
    def _register_tools(self):
        """Register basic sprite tools + CodeAgent tool"""
        # Register basic tools
        if self.sprites_available:
            self._register_basic_tools(self.sprites)
        
        # Register CodeAgent tool
        if self.codeagent_available:
            self._register_codeagent_tool()
        
        # Register placeholder if nothing available
        if not self.sprites_available and not self.codeagent_available:
            @self.mcp.tool()
            def sprites_unavailable() -> dict:
                """Placeholder tool when sprites are not available"""
                return {"error": "Sprites and CodeAgent not available - missing dependencies"}
            
            self.tool_registry = {
                "sprites_unavailable": {
                    "name": "sprites_unavailable", 
                    "description": "Placeholder tool when sprites are not available"
                }
            }
        
        print(f"✓ Level 2: Registered {len(self.tool_registry)} tools", file=sys.stderr)
    
    def _register_codeagent_tool(self):
        """Register the sprite CodeAgent as an MCP tool"""
        
        @self.mcp.tool()
        def sprite_codeagent_tool(
            algorithm_request: str, 
            model_id: str = "anthropic/claude-sonnet-4-20250514",
            stream_outputs: bool = False
        ) -> dict:
            """
            AI CodeAgent that writes Python algorithms using all Gaia sprites.
            
            This tool can generate and execute complex Python code that:
            - Loops through sprite results
            - Combines data from multiple sources  
            - Makes decisions based on data
            - Orchestrates sophisticated workflows
            
            Args:
                algorithm_request: Natural language description of the algorithm to execute
                model_id: LLM model to use for code generation
                stream_outputs: Whether to stream outputs during execution
            
            Returns:
                dict: Result of the algorithm execution or error information
            """
            try:
                # Use the one-liner function for simplicity
                result = self.run_sprite_algorithm(
                    algorithm_request,
                    model_id=model_id,
                    stream_outputs=stream_outputs
                )
                
                return {
                    "tool": "sprite_codeagent",
                    "algorithm_request": algorithm_request,
                    "model_id": model_id,
                    "stream_outputs": stream_outputs,
                    "result": result,
                    "success": True
                }
                
            except Exception as e:
                return {
                    "tool": "sprite_codeagent",
                    "algorithm_request": algorithm_request,
                    "error": str(e),
                    "success": False
                }
        
        # Add to tool registry
        self.tool_registry["sprite_codeagent_tool"] = {
            "name": "sprite_codeagent_tool",
            "description": "AI CodeAgent that writes Python algorithms using all Gaia sprites"
        }

if __name__ == "__main__":
    SpriteMCPServerLevel2.main()
