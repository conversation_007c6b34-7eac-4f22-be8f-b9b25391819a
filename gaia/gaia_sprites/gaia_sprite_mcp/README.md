# Gaia Sprite MCP Server - Modular Architecture

Model Context Protocol (MCP) server for Gaia Sprites with modular architecture supporting different capability levels and HTTP streaming protocol.

## Overview

This package provides a modular MCP server architecture with:

### 🏗️ **Server Levels**
- **Level 1** - Basic sprite tools only (11 tools)
- **Level 2** - Basic sprites + AI CodeAgent (12 tools)

### 🌐 **Transport Protocols**
- **HTTP Streaming** (default) - Production deployment and web integration
- **Stdio Protocol** - MCP client compatibility

### 📦 **Architecture**
- **Base Class** - Common functionality and HTTP/stdio support
- **Level Servers** - Specific tool configurations
- **Interchangeable** - Use Level 1 or Level 2 as needed

## Tool Comparison

### Level 1 Tools (11 Basic Sprites)
- `agsearch` - Search AgFunder company database
- `market_research_tool` - Get market research data (TAM/CAGR)
- `web_search_tool` - Multi-source web search (web, news, scholar, patents)
- `news_search_tool` - Specialized news search
- `omni_search_tool` - Internal omni database search
- `wiki_search_tool` - Wikipedia search and summaries
- `multimodel_query_tool` - Query multiple LLM models
- `frame_search_tool` - Search data frames with text query
- `frame_list_tool` - List available parquet data frames
- `investor_search_tool` - Search for investors with filters
- `investor_stats_tool` - Get detailed investor statistics

### Level 2 Tools (11 Basic + 1 AI Agent)
**All Level 1 tools PLUS:**
- `sprite_codeagent_tool` - AI CodeAgent that writes Python algorithms using all sprites

## Available Tools

The MCP server exposes the following sprite tools:

### AgFunder Internal Tools
- **`agsearch`** - Search AgFunder company database
- **`omni_search_tool`** - Search internal omni database  
- **`frame_search_tool`** - Search data frames with text queries
- **`frame_list_tool`** - List available parquet data frames
- **`investor_search_tool`** - Search for investors with filters
- **`investor_stats_tool`** - Get detailed investor statistics

### External Data Tools
- **`web_search_tool`** - Multi-source web search (web, news, scholar, patents)
- **`news_search_tool`** - Specialized news search
- **`wiki_search_tool`** - Wikipedia search and summaries

### AI/LLM Tools
- **`multimodel_query_tool`** - Query multiple LLM models in parallel
- **`market_research_tool`** - Get market research data (TAM/CAGR)

## Usage

### Server Selection

**Level 1 - Basic Sprites Only (11 tools):**
```bash
cd /SRC/agfunder/agbase_admin/gaia/gaia_sprites

# Default entry point (Level 1)
python3 gaia_sprite_mcp/sprite_mcp_server.py

# Explicit Level 1
python3 gaia_sprite_mcp/sprite_mcp_server_level1.py
```

**Level 2 - Basic Sprites + AI CodeAgent (12 tools):**
```bash
python3 gaia_sprite_mcp/sprite_mcp_server_level2.py
```

### Transport Options

**HTTP Streaming (Default - Production):**
```bash
# Level 1 HTTP server
python3 gaia_sprite_mcp/sprite_mcp_server_level1.py --transport http --port 8000

# Level 2 HTTP server
python3 gaia_sprite_mcp/sprite_mcp_server_level2.py --transport http --port 8001

# Custom host and port
python3 gaia_sprite_mcp/sprite_mcp_server_level1.py --host 0.0.0.0 --port 8080
```

**Stdio Protocol (MCP Clients):**
```bash
python3 gaia_sprite_mcp/sprite_mcp_server_level1.py --transport stdio
python3 gaia_sprite_mcp/sprite_mcp_server_level2.py --transport stdio
```

### Importing in Python

```python
from gaia_sprite_mcp import mcp, SPRITES_AVAILABLE

# Check if sprites are available
if SPRITES_AVAILABLE:
    print("All sprites loaded successfully")
    # Run the MCP server
    mcp.run()
else:
    print("Some sprites failed to load")
```

### Tool Examples

Each tool returns a standardized format with the tool name, input parameters, and results:

```python
# Example tool responses:
{
    "tool": "agsearch",
    "query": "vertical farming",
    "stats": {"total_results": 25, "hits": 150},
    "results": [...]
}

{
    "tool": "investor_search", 
    "name": "Sequoia",
    "country": "US",
    "results": [...]
}
```

## Architecture

### File Structure
```
gaia_sprite_mcp/
├── __init__.py              # Package initialization
├── sprite_mcp_server.py     # Main MCP server implementation
└── README.md               # This file
```

### Import Strategy
The MCP server automatically adds the parent directory to the Python path to import all sprites:

```python
# Add parent directory to path to import sprites
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)
```

### Error Handling
- Graceful handling of missing sprite dependencies
- Individual tool error handling with detailed error messages
- Fallback placeholder tools when sprites are unavailable

## Dependencies

The MCP server requires:
- `mcp.server.fastmcp` - FastMCP framework
- All sprite dependencies (varies by sprite)

## Development

### Adding New Tools
To add a new sprite tool:

1. Import the sprite function at the top of `sprite_mcp_server.py`
2. Add a new `@mcp.tool()` decorated function
3. Follow the standard return format with tool name, parameters, and results

### Testing
Test the MCP server import:

```python
from gaia_sprite_mcp import mcp, SPRITES_AVAILABLE
print(f"Sprites available: {SPRITES_AVAILABLE}")
```

## Integration

This MCP server can be integrated with:
- Claude Desktop (via MCP configuration)
- Other MCP-compatible AI assistants
- Custom applications using the MCP protocol

## Notes

- The server automatically detects available sprites and handles missing dependencies
- All tools follow the same input/output pattern for consistency
- Error handling ensures the server remains stable even if individual sprites fail
- The server supports both development and production environments
