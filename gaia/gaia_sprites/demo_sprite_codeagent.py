#!/usr/bin/env python3
"""
Demo: Sprite CodeAgent

This demo shows how to use the Sprite CodeAgent to write Python algorithms
that orchestrate multiple sprite calls for complex data analysis.

The CodeAgent can write Python code that:
- Loops through sprite results
- Combines data from multiple sources
- Performs complex analysis workflows
- Makes decisions based on data
"""

def demo_basic_usage():
    """Demo basic sprite code agent usage"""
    print("🎯 Demo: Basic Sprite CodeAgent Usage")
    print("=" * 50)
    
    try:
        from sprite_codeagent import create_sprite_codeagent
        
        # This would create an agent with all sprite tools
        print("Creating sprite code agent...")
        print("(Note: Requires smolagents[mcp] and API keys)")
        
        # Example of what the agent could do:
        example_query = """
        # Multi-step AgTech research algorithm
        
        # Step 1: Find companies
        companies = agsearch("vertical farming", limit=10)
        print(f"Found {len(companies['results'])} companies")
        
        # Step 2: Get market data
        market_data = market_research_tool("vertical farming")
        print(f"Market size: {market_data.get('market_size', 'N/A')}")
        
        # Step 3: Search for recent news
        news = web_search_tool("vertical farming 2024", sources=["news"], num_results=5)
        
        # Step 4: Analyze top companies
        top_companies = []
        for company in companies["results"][:3]:
            # Get company news
            company_news = web_search_tool(f'{company["name"]} funding', sources=["news"])
            
            # Get investor info if available
            if "investors" in company:
                investor_stats = investor_search_tool(company["investors"][0])
            
            top_companies.append({
                "name": company["name"],
                "description": company.get("description", ""),
                "news_count": len(company_news.get("results", [])),
                "has_recent_funding": any("funding" in news.get("title", "").lower() 
                                        for news in company_news.get("results", []))
            })
        
        # Step 5: Generate insights using multiple models
        insights = multimodel_query_tool(
            f"Analyze these vertical farming companies and market data: {top_companies}, {market_data}",
            models=["claude", "gpt-4"]
        )
        
        return {
            "companies_analyzed": len(top_companies),
            "market_data": market_data,
            "insights": insights,
            "top_companies": top_companies
        }
        """
        
        print("\nExample algorithm the agent could execute:")
        print(example_query)
        
        print("\n✓ This demonstrates how the CodeAgent can:")
        print("  - Loop through sprite results")
        print("  - Combine data from multiple sources")
        print("  - Make conditional decisions")
        print("  - Orchestrate complex workflows")
        
    except ImportError as e:
        print(f"✗ Import failed (expected in demo): {e}")
        print("To run this demo, install: pip install smolagents[mcp]")

def demo_algorithm_examples():
    """Show various algorithm examples the CodeAgent could execute"""
    print("\n🔍 Demo: Algorithm Examples")
    print("=" * 50)
    
    algorithms = [
        {
            "name": "Competitive Analysis",
            "description": "Compare companies in the same sector",
            "code": """
# Find competitors in AgTech
agtech_companies = agsearch("agriculture technology", limit=20)

# Analyze each company
competitor_analysis = []
for company in agtech_companies["results"]:
    # Get funding data
    funding_news = web_search_tool(f'{company["name"]} funding', sources=["news"])
    
    # Get investor information
    if "funding_total" in company:
        investors = investor_search_tool(company["name"])
    
    # Get market position
    market_news = web_search_tool(f'{company["name"]} market share', sources=["web"])
    
    competitor_analysis.append({
        "name": company["name"],
        "funding_total": company.get("funding_total", 0),
        "recent_news_count": len(funding_news.get("results", [])),
        "market_mentions": len(market_news.get("results", []))
    })

# Rank by funding and news activity
ranked_competitors = sorted(competitor_analysis, 
                          key=lambda x: (x["funding_total"], x["recent_news_count"]), 
                          reverse=True)

# Generate competitive insights
insights = multimodel_query_tool(
    f"Analyze competitive landscape: {ranked_competitors[:5]}"
)
"""
        },
        {
            "name": "Investment Trend Analysis", 
            "description": "Analyze investment patterns across sectors",
            "code": """
# Get top investors in AgTech
agtech_investors = investor_search_tool("agriculture", investor_type="venture", limit=10)

# Analyze investment patterns
investment_analysis = {}
for investor in agtech_investors["results"]:
    # Get detailed stats
    stats = investor_stats_tool(investor["name"])
    
    # Get recent portfolio companies
    portfolio = investor_portfolio_tool(investor["name"], limit=20)
    
    # Analyze sector focus
    sectors = {}
    for company in portfolio.get("results", []):
        sector = company.get("sector", "Unknown")
        sectors[sector] = sectors.get(sector, 0) + 1
    
    investment_analysis[investor["name"]] = {
        "total_investments": stats.get("total_investments", 0),
        "avg_investment": stats.get("avg_investment_usd_mm", 0),
        "sector_focus": max(sectors.items(), key=lambda x: x[1]) if sectors else None,
        "geographic_focus": stats.get("home_country", "Unknown")
    }

# Find investment trends
trends = multimodel_query_tool(
    f"Identify investment trends from this data: {investment_analysis}"
)
"""
        },
        {
            "name": "Market Opportunity Mapping",
            "description": "Map market opportunities using multiple data sources", 
            "code": """
# Define sectors to analyze
sectors = ["vertical farming", "precision agriculture", "food tech", "sustainable packaging"]

market_opportunities = {}
for sector in sectors:
    # Get market research data
    market_data = market_research_tool(sector)
    
    # Find companies in sector
    companies = agsearch(sector, limit=15)
    
    # Get recent news and trends
    news = web_search_tool(f"{sector} trends 2024", sources=["news"], num_results=10)
    
    # Analyze funding activity
    funding_news = web_search_tool(f"{sector} funding investment", sources=["news"])
    
    # Get Wikipedia context
    wiki_info = wiki_search_tool(sector, sentences=5)
    
    market_opportunities[sector] = {
        "market_size": market_data.get("market_size_usd_bn", 0),
        "growth_rate": market_data.get("cagr_percent", 0),
        "company_count": len(companies.get("results", [])),
        "news_mentions": len(news.get("results", [])),
        "funding_activity": len(funding_news.get("results", [])),
        "market_maturity": "emerging" if len(companies.get("results", [])) < 10 else "established"
    }

# Rank opportunities
ranked_opportunities = sorted(market_opportunities.items(),
                            key=lambda x: (x[1]["growth_rate"], x[1]["market_size"]),
                            reverse=True)

# Generate opportunity insights
opportunity_report = multimodel_query_tool(
    f"Analyze market opportunities and recommend investment focus: {ranked_opportunities}"
)
"""
        }
    ]
    
    for i, algo in enumerate(algorithms, 1):
        print(f"\n{i}. {algo['name']}")
        print(f"   {algo['description']}")
        print("   Code preview:")
        print("   " + "\n   ".join(algo['code'].split('\n')[:10]))
        print("   ...")

def demo_integration_patterns():
    """Show integration patterns with the CodeAgent"""
    print("\n🔗 Demo: Integration Patterns")
    print("=" * 50)
    
    patterns = [
        {
            "name": "One-liner Execution",
            "code": """
from sprite_codeagent import run_sprite_algorithm

result = run_sprite_algorithm("Find top 5 sustainable agriculture companies and their funding")
print(result)
"""
        },
        {
            "name": "Persistent Agent Session",
            "code": """
from sprite_codeagent import create_sprite_codeagent

agent = create_sprite_codeagent()

# Run multiple related queries
companies = agent.run("Find vertical farming companies")
investors = agent.run("Find investors in these companies: " + str(companies))
analysis = agent.run("Analyze investment patterns: " + str(investors))
"""
        },
        {
            "name": "Custom Model Configuration",
            "code": """
from sprite_codeagent import create_sprite_codeagent

# Use specific model
agent = create_sprite_codeagent(
    model_id="anthropic/claude-sonnet-4-20250514",
    stream_outputs=True
)

result = agent.run("Complex multi-step analysis...")
"""
        },
        {
            "name": "Error Handling",
            "code": """
from sprite_codeagent import run_sprite_algorithm

try:
    result = run_sprite_algorithm("Your query here")
    if result.get("success"):
        print("Success:", result["result"])
    else:
        print("Error:", result["error"])
except Exception as e:
    print("Exception:", e)
"""
        }
    ]
    
    for pattern in patterns:
        print(f"\n{pattern['name']}:")
        print(pattern['code'])

if __name__ == "__main__":
    """Run all demos"""
    print("🤖 Sprite CodeAgent Demo")
    print("=" * 60)
    
    demo_basic_usage()
    demo_algorithm_examples()
    demo_integration_patterns()
    
    print("\n🎯 Summary")
    print("=" * 50)
    print("The Sprite CodeAgent enables:")
    print("✓ Complex multi-step algorithms using all Gaia sprites")
    print("✓ Python code generation for data analysis workflows")
    print("✓ Automatic orchestration of multiple data sources")
    print("✓ Intelligent decision-making based on data")
    print("✓ Scalable research and analysis automation")
    
    print("\nTo get started:")
    print("1. Install dependencies: pip install smolagents[mcp]")
    print("2. Set up API keys for your preferred LLM")
    print("3. Import and use: from sprite_codeagent import run_sprite_algorithm")
    print("4. Run algorithms: result = run_sprite_algorithm('Your query')")
