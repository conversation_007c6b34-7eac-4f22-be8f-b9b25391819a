# Sprite CodeAgent

🤖 **AI Agent that writes Python algorithms using all Gaia sprites as tools**

## Overview

The Sprite CodeAgent is an advanced AI agent that can write complex Python algorithms involving sprite calls. Unlike simple tool calling, it generates and executes Python code that can loop, combine data sources, make decisions, and orchestrate sophisticated workflows using all available Gaia sprites.

## Architecture

```
LLM Model → CodeAgent → MCPAdapt → SmolAgentsAdapter → Sprite MCP Server → Individual Sprites
```

**Key Components:**
- **smolagents CodeAgent** - AI agent that thinks in Python code
- **MCPAdapt** - Bridges MCP servers to agentic frameworks  
- **SmolAgentsAdapter** - Converts MCP tools to smolagents format
- **Gaia Sprite MCP Server** - Exposes all 11 sprites as MCP tools

## Features

### 🧠 **Intelligent Code Generation**
- Writes Python algorithms, not just tool calls
- Uses loops, conditionals, and data processing
- Makes decisions based on intermediate results
- Combines multiple data sources intelligently

### 🔄 **Dynamic Workflows**
- Adapts algorithms based on data
- Handles errors and edge cases
- Optimizes queries based on results
- Scales analysis automatically

### 📊 **Multi-Source Analysis**
- Orchestrates all 11 Gaia sprites
- Combines internal and external data
- Cross-references multiple sources
- Generates comprehensive insights

## Available Sprite Tools (11)

1. **`agsearch`** - AgFunder company database
2. **`market_research_tool`** - TAM/CAGR market data
3. **`web_search_tool`** - Multi-source web search
4. **`news_search_tool`** - Specialized news search
5. **`wiki_search_tool`** - Wikipedia search and summaries
6. **`omni_search_tool`** - Internal omni database
7. **`frame_search_tool`** - Data frame search
8. **`frame_list_tool`** - List available data frames
9. **`investor_search_tool`** - Investor search with filters
10. **`investor_stats_tool`** - Detailed investor analytics
11. **`multimodel_query_tool`** - Multi-LLM consensus queries

## Installation

```bash
# Install dependencies
pip install smolagents[mcp] mcpadapt

# Verify installation
python3 test_mcpadapt_integration.py
```

## Usage

### 🚀 **One-liner Execution**
```python
from sprite_codeagent import run_sprite_algorithm

# Stdio transport (default - starts own server)
result = run_sprite_algorithm("Find top 5 AgTech companies and analyze their funding patterns")

# HTTP transport (connects to existing server)
result = run_sprite_algorithm(
    "Find top 5 AgTech companies and analyze their funding patterns",
    transport="http",
    server_url="http://localhost:8000"
)
print(result)
```

### 🔧 **Persistent Agent Session**
```python
from sprite_codeagent import create_sprite_codeagent

# Stdio transport (default)
agent = create_sprite_codeagent()

# HTTP transport (requires running server)
agent = create_sprite_codeagent(transport="http", server_url="http://localhost:8000")

# Run multiple related queries
companies = agent.run("Find vertical farming companies")
investors = agent.run("Find investors in these companies: " + str(companies))
analysis = agent.run("Analyze investment patterns: " + str(investors))
```

### ⚙️ **Custom Configuration**
```python
agent = create_sprite_codeagent(
    model_id="anthropic/claude-sonnet-4-20250514",
    stream_outputs=True
)

result = agent.run("Complex multi-step analysis...")
```

## Command Line Interface

```bash
# Show usage examples
python3 sprite_codeagent.py --examples

# Run a query
python3 sprite_codeagent.py --query "Analyze AgTech investment trends"

# Run demo
python3 demo_sprite_codeagent.py

# Test integration
python3 test_mcpadapt_integration.py
```

## Algorithm Examples

### 🔍 **Multi-Source Research**
```python
# The agent can write code like this:
companies = agsearch("vertical farming", limit=10)
market_data = market_research_tool("vertical farming")
news = web_search_tool("vertical farming 2024", sources=["news"])

for company in companies["results"][:5]:
    company_news = web_search_tool(f'{company["name"]} funding', sources=["news"])
    # Process and correlate data...

insights = multimodel_query_tool("Analyze trends", models=["claude", "gpt-4"])
```

### 📈 **Investment Analysis**
```python
# Automated investment pattern analysis:
investors = investor_search_tool("agriculture", investor_type="venture")
for investor in investors["results"][:3]:
    stats = investor_stats_tool(investor["name"])
    portfolio = investor_portfolio_tool(investor["name"])
    # Analyze patterns...
```

### 🌐 **Market Opportunity Mapping**
```python
# Cross-sector opportunity analysis:
sectors = ["vertical farming", "precision agriculture", "food tech"]
for sector in sectors:
    market_data = market_research_tool(sector)
    companies = agsearch(sector, limit=15)
    news = web_search_tool(f"{sector} trends 2024")
    # Rank opportunities...
```

## Model Support

**Supported LLM Backends:**
- **LiteLLM** - OpenAI, Anthropic, Google, etc.
- **InferenceClient** - Hugging Face models
- **Transformers** - Local models

**Recommended Models:**
- `anthropic/claude-sonnet-4-20250514` (default)
- `openai/gpt-4o`
- `meta-llama/Llama-2-70b-chat-hf`

## Error Handling

```python
try:
    result = run_sprite_algorithm("Your query here")
    if result.get("success"):
        print("Success:", result["result"])
    else:
        print("Error:", result["error"])
except Exception as e:
    print("Exception:", e)
```

## Files

- **`sprite_codeagent.py`** - Main CodeAgent implementation
- **`demo_sprite_codeagent.py`** - Comprehensive demos
- **`test_mcpadapt_integration.py`** - Integration tests
- **`gaia_sprite_mcp/`** - MCP server package

## Benefits

- **🧠 Intelligent Orchestration** - AI writes the algorithms
- **🔄 Dynamic Workflows** - Adapts based on data
- **📊 Multi-Source Analysis** - Combines all sprites
- **⚡ Scalable Research** - Automates complex workflows
- **🎯 Business Intelligence** - Generates actionable insights

## Requirements

- Python 3.8+
- smolagents[mcp]
- mcpadapt
- API keys for chosen LLM model
- Gaia sprite dependencies

## Getting Started

1. **Install dependencies:**
   ```bash
   pip install smolagents[mcp] mcpadapt
   ```

2. **Set up API keys:**
   ```bash
   export ANTHROPIC_API_KEY="your-key"
   # or OPENAI_API_KEY, etc.
   ```

3. **Test the setup:**
   ```bash
   python3 test_mcpadapt_integration.py
   ```

4. **Run your first algorithm:**
   ```python
   from sprite_codeagent import run_sprite_algorithm
   result = run_sprite_algorithm("Find top sustainable agriculture companies")
   ```

The Sprite CodeAgent transforms the Gaia sprite ecosystem into a **programmable research and analysis platform** powered by AI! 🚀
