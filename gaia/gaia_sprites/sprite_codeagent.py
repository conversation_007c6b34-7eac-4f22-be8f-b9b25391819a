#!/usr/bin/env python3
"""
Sprite Code Agent

A code agent that can write Python algorithms involving sprite calls using MCPAdapt.
This agent has access to all Gaia sprites as tools and can write complex Python code
to loop, combine, and orchestrate sprite calls for sophisticated data analysis.

Based on:
- https://github.com/grll/mcpadapt
- https://grll.github.io/mcpadapt/
- smolagents CodeAgent framework

pip install smolagents[mcp]

"""

import sys
import os
from typing import List, Optional, Any

def create_sprite_codeagent(
    model_id: str = "anthropic/claude-sonnet-4-20250514",
    stream_outputs: bool = True,
    transport: str = "stdio",
    server_url: str = "http://localhost:8000"
):
    """
    Create a CodeAgent with access to all Gaia sprites as tools via MCP.

    Args:
        model_id: LLM model to use (default: Claude Sonnet 4)
        stream_outputs: Whether to stream outputs
        transport: Transport protocol - "stdio" or "http" (default: stdio)
        server_url: HTTP server URL if using http transport

    Returns:
        CodeAgent instance with all sprite tools loaded
    """
    try:
        # Import required libraries
        from smolagents import CodeAgent
        from mcp import ClientSession, StdioServerParameters
        from mcpadapt.core import MCPAdapt
        from mcpadapt.smolagents_adapter import SmolAgentsAdapter
        import httpx

        # Try different model imports based on what's available
        model = None

        # Try LiteLLM first (supports most models)
        try:
            from smolagents import LiteLLMModel
            model = LiteLLMModel(model_id=model_id)
            print(f"✓ Using LiteLLM model: {model_id}")
        except ImportError:
            print("LiteLLM not available, trying InferenceClientModel...")

        # Fallback to InferenceClientModel
        if model is None:
            try:
                from smolagents import InferenceClientModel
                model = InferenceClientModel(model_id=model_id)
                print(f"✓ Using InferenceClient model: {model_id}")
            except ImportError:
                print("InferenceClientModel not available, trying TransformersModel...")

        # Last fallback to TransformersModel
        if model is None:
            try:
                from smolagents import TransformersModel
                model = TransformersModel(model_id=model_id)
                print(f"✓ Using Transformers model: {model_id}")
            except ImportError:
                raise ImportError("No compatible model backend found. Install smolagents[litellm] or smolagents[transformers]")

        # Configure MCP server connection based on transport
        if transport.lower() == "http":
            # HTTP transport - connect to existing server
            try:
                import httpx
                # Test if server is running
                response = httpx.get(f"{server_url}/health", timeout=2.0)
                if response.status_code == 200:
                    print(f"✓ Using HTTP transport: {server_url}")
                    # For HTTP, we'll need to implement HTTP-based tool loading
                    # This is a more complex implementation that would require
                    # custom HTTP client integration with MCPAdapt
                    raise NotImplementedError("HTTP transport not yet fully implemented. Use transport='stdio' for now.")
                else:
                    raise ConnectionError(f"HTTP server not responding at {server_url}")
            except Exception as e:
                print(f"✗ HTTP transport failed: {e}")
                print("Falling back to stdio transport...")
                transport = "stdio"

        if transport.lower() == "stdio":
            # Stdio transport - start our own server process
            current_dir = os.path.dirname(os.path.abspath(__file__))
            mcp_server_path = os.path.join(current_dir, "gaia_sprite_mcp", "sprite_mcp_server_level1.py")

            if not os.path.exists(mcp_server_path):
                raise FileNotFoundError(f"MCP server not found at: {mcp_server_path}")

            server_params = StdioServerParameters(
                command="python3",
                args=[mcp_server_path, "--transport", "stdio"]
            )
            print("✓ Using Level 1 MCP server with stdio transport")
        else:
            raise ValueError(f"Unsupported transport: {transport}. Use 'stdio' or 'http'.")

        print("🔧 Loading Gaia sprites via MCPAdapt...")

        # Use MCPAdapt with SmolAgentsAdapter to load tools
        with MCPAdapt(server_params, SmolAgentsAdapter()) as tools:
            print(f"✓ Loaded {len(tools)} sprite tools:")
            for tool in tools:
                print(f"  - {tool.name}: {tool.description}")

            # Create the CodeAgent with all sprite tools
            agent = CodeAgent(
                tools=tools,
                model=model,
                stream_outputs=stream_outputs
            )

            print("🤖 Sprite CodeAgent created successfully!")
            return agent

    except Exception as e:
        print(f"✗ Error creating sprite code agent: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_sprite_algorithm(
    query: str,
    model_id: str = "anthropic/claude-sonnet-4-20250514",
    stream_outputs: bool = True,
    transport: str = "stdio",
    server_url: str = "http://localhost:8000"
):
    """
    One-liner to run a sprite algorithm using the code agent.

    Args:
        query: The task/algorithm to execute
        model_id: LLM model to use
        stream_outputs: Whether to stream outputs
        transport: Transport protocol - "stdio" or "http"
        server_url: HTTP server URL if using http transport

    Returns:
        Result from the agent execution
    """
    try:
        agent = create_sprite_codeagent(
            model_id=model_id,
            stream_outputs=stream_outputs,
            transport=transport,
            server_url=server_url
        )
        if agent is None:
            return {"error": "Failed to create sprite code agent"}

        print(f"🚀 Running sprite algorithm: {query}")
        result = agent.run(query)
        return {"result": result, "success": True}

    except Exception as e:
        return {"error": f"Error running sprite algorithm: {str(e)}", "success": False}

def demo_sprite_algorithms():
    """
    Demo function showing various sprite algorithm examples.
    """
    print("🎯 Sprite CodeAgent Demo")
    print("=" * 50)

    # Create the agent once
    agent = create_sprite_codeagent()
    if agent is None:
        print("Failed to create agent")
        return

    # Demo algorithms
    demos = [
        {
            "name": "Multi-Source Research",
            "query": """
            Research 'vertical farming' using multiple sources:
            1. Search AgFunder database for vertical farming companies
            2. Get market research data for vertical farming
            3. Search external web for latest vertical farming news
            4. Combine all results into a comprehensive report
            """
        },
        {
            "name": "Investor Analysis",
            "query": """
            Analyze top AgTech investors:
            1. Search for investors focused on 'agriculture technology'
            2. Get detailed stats for the top 3 investors
            3. Find co-investment patterns between them
            4. Create a summary of their investment strategies
            """
        },
        {
            "name": "Data Frame Analysis",
            "query": """
            Analyze company data:
            1. List available data frames
            2. Search for companies in the 'sustainability' sector
            3. Get statistics about the frame
            4. Create insights about company distribution by country
            """
        },
        {
            "name": "Multi-Model Consensus",
            "query": """
            Get consensus on 'Future of AgTech':
            1. Query multiple LLM models about AgTech trends
            2. Compare their responses
            3. Find consensus points
            4. Generate a unified prediction
            """
        }
    ]

    for i, demo in enumerate(demos, 1):
        print(f"\n🔍 Demo {i}: {demo['name']}")
        print("-" * 30)

        try:
            result = agent.run(demo['query'])
            print(f"✓ Result: {result}")
        except Exception as e:
            print(f"✗ Error: {e}")

        if i < len(demos):
            input("\nPress Enter to continue to next demo...")

# Usage examples and documentation
USAGE_EXAMPLES = """
# Sprite CodeAgent Usage Examples

## Basic Usage
```python
from sprite_codeagent import create_sprite_codeagent, run_sprite_algorithm

# Create agent with stdio transport (default)
agent = create_sprite_codeagent()

# Create agent with HTTP transport (requires running server)
agent = create_sprite_codeagent(transport="http", server_url="http://localhost:8000")

# Run algorithm
result = agent.run("Find top 5 AgTech companies and their funding rounds")
```

## One-liner Usage
```python
# Stdio transport (default)
result = run_sprite_algorithm("Compare Tesla and Rivian using multiple data sources")

# HTTP transport
result = run_sprite_algorithm(
    "Compare Tesla and Rivian using multiple data sources",
    transport="http",
    server_url="http://localhost:8000"
)
```

## Advanced Multi-Step Algorithms
```python
agent = create_sprite_codeagent()

# Complex research workflow
result = agent.run('''
# Multi-source AgTech analysis
companies = agsearch("vertical farming", limit=10)
market_data = market_research_tool("vertical farming")
news = web_search_tool("vertical farming 2024", sources=["news"])

# Combine and analyze
for company in companies["results"][:5]:
    company_news = web_search_tool(f'{company["name"]} funding', sources=["news"])
    # Process and correlate data...

# Generate insights
insights = multimodel_query_tool("Analyze vertical farming market trends", models=["claude", "gpt-4"])
''')
```

## Available Sprite Tools
- agsearch: AgFunder company database
- market_research_tool: TAM/CAGR data
- web_search_tool: Multi-source web search
- news_search_tool: News search
- wiki_search_tool: Wikipedia search
- omni_search_tool: Internal omni search
- frame_search_tool: Data frame search
- frame_list_tool: List data frames
- investor_search_tool: Investor search
- investor_stats_tool: Investor analytics
- multimodel_query_tool: Multi-LLM queries
"""

if __name__ == "__main__":
    """
    Command line interface for the sprite code agent.
    """
    import argparse

    parser = argparse.ArgumentParser(description="Sprite CodeAgent - AI agent with access to all Gaia sprites")
    parser.add_argument("--demo", action="store_true", help="Run demo algorithms")
    parser.add_argument("--query", type=str, help="Query to execute")
    parser.add_argument("--model", type=str, default="anthropic/claude-sonnet-4-20250514", help="Model to use")
    parser.add_argument("--no-stream", action="store_true", help="Disable streaming outputs")
    parser.add_argument("--examples", action="store_true", help="Show usage examples")

    args = parser.parse_args()

    if args.examples:
        print(USAGE_EXAMPLES)
    elif args.demo:
        demo_sprite_algorithms()
    elif args.query:
        result = run_sprite_algorithm(
            query=args.query,
            model_id=args.model,
            stream_outputs=not args.no_stream
        )
        print(f"Result: {result}")
    else:
        print("Sprite CodeAgent")
        print("Usage:")
        print("  python sprite_codeagent.py --demo")
        print("  python sprite_codeagent.py --query 'Find top AgTech companies'")
        print("  python sprite_codeagent.py --examples")
        print("  python sprite_codeagent.py --help")

