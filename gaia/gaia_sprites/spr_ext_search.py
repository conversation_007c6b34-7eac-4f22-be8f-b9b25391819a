# Simple 1-line interface for Web Search

def web_search(query="AI Agriculture", sources=['web'], num_results=10):
    """One-liner to perform web search using GaiaMultiSearch"""
    try:
        from gaia.multisearch_api import GaiaMultiSearch
        
        # Initialize the search client
        gms = GaiaMultiSearch()
        
        # Perform the search
        res_dict = gms.multisearch(query=query, sources=sources, num=num_results)
        
        # Extract and format results
        results = []
        for source in sources:
            if source in res_dict and 'organic_results' in res_dict[source]:
                for result in res_dict[source]['organic_results']:
                    # Clean up the result format
                    clean_result = {
                        'title': result.get('title', ''),
                        'link': result.get('link', ''),
                        'snippet': result.get('snippet', ''),
                        'source': source,
                        'position': result.get('position', 0)
                    }
                    results.append(clean_result)
        
        return results, {"total_results": len(results), "sources": sources}
        
    except Exception as e:
        print(f"Error in web_search: {e}")
        # Return empty results for testing when dependencies are missing
        return [], {"total_results": 0, "error": str(e)}

def web_search_advanced(query="AI Agriculture", sources=['web', 'scholar', 'news'], num_results=20):
    """Advanced web search with multiple sources"""
    return web_search(query=query, sources=sources, num_results=num_results)

def news_search(query="AI Agriculture", num_results=10):
    """Search for news articles only"""
    return web_search(query=query, sources=['news'], num_results=num_results)

def breaking_news(query="technology", num_results=20):
    """Search for recent breaking news"""
    return news_search(query=query, num_results=num_results)

def business_news(query="startups", num_results=15):
    """Search for business and startup news"""
    business_query = f"{query} business startup funding"
    return news_search(query=business_query, num_results=num_results)

def tech_news(query="AI", num_results=15):
    """Search for technology news"""
    tech_query = f"{query} technology innovation"
    return news_search(query=tech_query, num_results=num_results)

def agriculture_news(query="farming", num_results=15):
    """Search for agriculture and farming news"""
    ag_query = f"{query} agriculture farming agtech"
    return news_search(query=ag_query, num_results=num_results)

def climate_news(query="climate change", num_results=15):
    """Search for climate and environmental news"""
    climate_query = f"{query} climate environment sustainability"
    return news_search(query=climate_query, num_results=num_results)

def funding_news(query="Series A", num_results=15):
    """Search for funding and investment news"""
    funding_query = f"{query} funding investment venture capital"
    return news_search(query=funding_query, num_results=num_results)

def company_news(company_name="OpenAI", num_results=10):
    """Search for news about a specific company"""
    company_query = f'"{company_name}" company news'
    return news_search(query=company_query, num_results=num_results)

def industry_news(industry="fintech", num_results=15):
    """Search for news about a specific industry"""
    industry_query = f"{industry} industry market trends"
    return news_search(query=industry_query, num_results=num_results)

def scholar_search(query="AI Agriculture", num_results=10):
    """Search for academic papers only"""
    return web_search(query=query, sources=['scholar'], num_results=num_results)

def patents_search(query="AI Agriculture", num_results=10):
    """Search for patents only"""
    return web_search(query=query, sources=['patents'], num_results=num_results)

def get_news_summary(results):
    """Extract a summary from news search results"""
    if not results:
        return "No news articles found."

    summary = f"Found {len(results)} news articles:\n\n"
    for i, article in enumerate(results[:5], 1):  # Show top 5
        summary += f"{i}. {article.get('title', 'No title')}\n"
        summary += f"   Source: {article.get('source', 'Unknown')}\n"
        if article.get('date'):
            summary += f"   Date: {article.get('date')}\n"
        summary += f"   {article.get('snippet', 'No description')[:100]}...\n\n"

    return summary

# Usage examples:
# results, stats = web_search("DevOps automation tools")
# results, stats = web_search_advanced("Machine Learning", ['web', 'scholar'], 15)
# results, stats = news_search("Climate change agriculture")
# results, stats = breaking_news("climate change")
# results, stats = business_news("fintech startups")
# results, stats = tech_news("machine learning")
# results, stats = agriculture_news("vertical farming")
# results, stats = company_news("Tesla")
# results, stats = funding_news("Series B")
# results, stats = scholar_search("Artificial Intelligence farming")
# results, stats = patents_search("Autonomous tractors")
# summary = get_news_summary(results)

if __name__ == "__main__":
    """Test the external search sprite when run directly"""
    print("Testing spr_ext_search sprite...")

    # Test basic web search
    try:
        results, stats = web_search("AI Agriculture", num_results=5)
        print(f"✓ Basic web search test passed - found {stats.get('total_results', 0)} results")
        if results:
            print(f"Sample result: {results[0].get('title', 'N/A')}")
    except Exception as e:
        print(f"✗ Basic web search test failed: {e}")

    # Test news search
    try:
        results, stats = news_search("Climate change", num_results=3)
        print(f"✓ News search test passed - found {stats.get('total_results', 0)} results")
    except Exception as e:
        print(f"✗ News search test failed: {e}")

    # Test business news
    try:
        results, stats = business_news("fintech", num_results=3)
        print(f"✓ Business news test passed - found {stats.get('total_results', 0)} results")
    except Exception as e:
        print(f"✗ Business news test failed: {e}")

    # Test company news
    try:
        results, stats = company_news("OpenAI", num_results=3)
        print(f"✓ Company news test passed - found {stats.get('total_results', 0)} results")
    except Exception as e:
        print(f"✗ Company news test failed: {e}")

    # Test advanced search
    try:
        results, stats = web_search_advanced("Machine Learning", ['web', 'scholar'], 5)
        print(f"✓ Advanced search test passed - found {stats.get('total_results', 0)} results")
    except Exception as e:
        print(f"✗ Advanced search test failed: {e}")

    print("spr_ext_search test complete.")
