# Simple 1-line interface for Gaia Data Frames

def frame_search(query="Tesla", section_slug="agbase", frame_slug="agbase_org__slim", limit=100):
    """One-liner to search data frames with text query"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import duckdb
        
        # Get frame path
        frame_path = gaia_frames.get_frame_path(section_slug=section_slug, frame_slug=frame_slug)
        
        if not os.path.exists(frame_path):
            return [], {"error": f"Frame not found: {frame_path}", "total_results": 0}
        
        # Build search query
        search_query = f"""
        SELECT *
        FROM '{frame_path}'
        WHERE
        (name ILIKE '%{query}%' OR short_description ILIKE '%{query}%' OR
         description ILIKE '%{query}%' OR keywords ILIKE '%{query}%')
        ORDER BY name ASC
        LIMIT {limit}
        """
        
        # Execute query
        df = duckdb.query(search_query).df()
        
        # Convert to list of dictionaries
        results = df.to_dict('records')
        
        stats = {
            "total_results": len(results),
            "query": query,
            "section_slug": section_slug,
            "frame_slug": frame_slug,
            "frame_path": frame_path
        }
        
        return results, stats
        
    except Exception as e:
        return [], {"error": f"Error in frame_search: {str(e)}", "total_results": 0}

def frame_query(section_slug="agbase", frame_slug="agbase_org__slim", sql_query="SELECT * FROM frame LIMIT 10"):
    """Execute custom SQL query on a data frame"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import duckdb
        
        # Get frame path
        frame_path = gaia_frames.get_frame_path(section_slug=section_slug, frame_slug=frame_slug)
        
        if not os.path.exists(frame_path):
            return [], {"error": f"Frame not found: {frame_path}", "total_results": 0}
        
        # Replace 'frame' placeholder with actual path
        actual_query = sql_query.replace('frame', f"'{frame_path}'")
        
        # Execute query
        df = duckdb.query(actual_query).df()
        
        # Convert to list of dictionaries
        results = df.to_dict('records')
        
        stats = {
            "total_results": len(results),
            "sql_query": actual_query,
            "section_slug": section_slug,
            "frame_slug": frame_slug,
            "frame_path": frame_path
        }
        
        return results, stats
        
    except Exception as e:
        return [], {"error": f"Error in frame_query: {str(e)}", "total_results": 0}

def frame_list(root_dir="/var/lib/gaia/GAIA_FS/frames/"):
    """List available parquet data frames"""
    try:
        import os
        
        def path_to_slug(path):
            slug = path.replace('/', '-')
            if slug.startswith('-'):
                slug = slug[1:]
            return slug
        
        # Find parquet files
        parquet_paths = []
        matching_files = os.popen(f"find {root_dir} -type f -name '*.parquet'").read().split('\n')
        
        for full_path in matching_files:
            if full_path.endswith('.parquet'):
                # Skip part files with non-zero numbers
                if 'part' in full_path:
                    if any(char.isdigit() for char in full_path if char != '0'):
                        continue
                
                slug = path_to_slug(full_path)
                partial_slug = '/' + '/'.join([aa.replace('.parquet', '') for aa in full_path.split('/')[6:]])
                
                frame_info = {
                    'full_path': full_path,
                    'slug': slug,
                    'partial_slug': partial_slug,
                    'size_mb': round(os.path.getsize(full_path) / (1024*1024), 2) if os.path.exists(full_path) else 0
                }
                parquet_paths.append(frame_info)
        
        # Sort by partial slug
        parquet_paths.sort(key=lambda x: x['partial_slug'])
        
        stats = {
            "total_frames": len(parquet_paths),
            "root_dir": root_dir
        }
        
        return parquet_paths, stats
        
    except Exception as e:
        return [], {"error": f"Error in frame_list: {str(e)}", "total_frames": 0}

def frame_read(section_slug="agbase", frame_slug="agbase_org__slim", nrows=100, cols=None):
    """Read a data frame using gaia_frames"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        
        # Read frame
        df = gaia_frames.read_frame_pandas(
            section_slug=section_slug,
            frame_slug=frame_slug,
            nrows=nrows,
            cols=cols
        )
        
        # Convert to list of dictionaries
        results = df.to_dict('records')
        
        stats = {
            "total_results": len(results),
            "section_slug": section_slug,
            "frame_slug": frame_slug,
            "nrows_requested": nrows,
            "columns": list(df.columns) if df is not None else [],
            "shape": df.shape if df is not None else (0, 0)
        }
        
        return results, stats
        
    except Exception as e:
        return [], {"error": f"Error in frame_read: {str(e)}", "total_results": 0}

def goid_lookup(goid="G0__org__cbuuid__12345", section_slug="agbase", frame_slug="agbase_org__slim"):
    """Look up a record by GOID"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import duckdb
        
        # Get frame path
        frame_path = gaia_frames.get_frame_path(section_slug=section_slug, frame_slug=frame_slug)
        
        if not os.path.exists(frame_path):
            return [], {"error": f"Frame not found: {frame_path}", "total_results": 0}
        
        # Build GOID query
        goid_query = f"""
        SELECT *
        FROM '{frame_path}'
        WHERE GOID = '{goid}'
        """
        
        # Execute query
        df = duckdb.query(goid_query).df()
        
        # Convert to list of dictionaries
        results = df.to_dict('records')
        
        stats = {
            "total_results": len(results),
            "goid": goid,
            "section_slug": section_slug,
            "frame_slug": frame_slug,
            "found": len(results) > 0
        }
        
        return results, stats
        
    except Exception as e:
        return [], {"error": f"Error in goid_lookup: {str(e)}", "total_results": 0}

def frame_stats(section_slug="agbase", frame_slug="agbase_org__slim"):
    """Get statistics about a data frame"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import duckdb
        
        # Get frame path
        frame_path = gaia_frames.get_frame_path(section_slug=section_slug, frame_slug=frame_slug)
        
        if not os.path.exists(frame_path):
            return {}, {"error": f"Frame not found: {frame_path}"}
        
        # Get basic stats
        count_query = f"SELECT count(*) as total_rows FROM '{frame_path}'"
        count_df = duckdb.query(count_query).df()
        total_rows = count_df.iloc[0]['total_rows']
        
        # Get column info
        columns_query = f"DESCRIBE SELECT * FROM '{frame_path}'"
        columns_df = duckdb.query(columns_query).df()
        
        file_size_mb = round(os.path.getsize(frame_path) / (1024*1024), 2)
        
        stats = {
            "section_slug": section_slug,
            "frame_slug": frame_slug,
            "frame_path": frame_path,
            "total_rows": int(total_rows),
            "total_columns": len(columns_df),
            "file_size_mb": file_size_mb,
            "columns": columns_df.to_dict('records')
        }
        
        return stats, {"success": True}
        
    except Exception as e:
        return {}, {"error": f"Error in frame_stats: {str(e)}"}

# Usage examples:
# results, stats = frame_search("Tesla", section_slug="agbase", frame_slug="agbase_org__slim")
# results, stats = frame_query(section_slug="agbase", frame_slug="agbase_org__slim", sql_query="SELECT name, description FROM frame WHERE country_code='US' LIMIT 10")
# frames, stats = frame_list()
# results, stats = frame_read(section_slug="agbase", frame_slug="agbase_org__slim", nrows=50)
# results, stats = goid_lookup("G0__org__cbuuid__12345")
# stats, info = frame_stats(section_slug="agbase", frame_slug="agbase_org__slim")

if __name__ == "__main__":
    """Test the frames sprite when run directly"""
    print("Testing spr_agf_frames sprite...")
    
    # Test frame list
    try:
        frames, stats = frame_list()
        print(f"✓ Frame list test passed - found {stats.get('total_frames', 0)} frames")
        if frames:
            print(f"Sample frame: {frames[0].get('partial_slug', 'N/A')}")
    except Exception as e:
        print(f"✗ Frame list test failed: {e}")
    
    # Test frame stats
    try:
        stats, info = frame_stats(section_slug="agbase", frame_slug="agbase_org__slim")
        if info.get('success'):
            print(f"✓ Frame stats test passed - {stats.get('total_rows', 0)} rows, {stats.get('total_columns', 0)} columns")
        else:
            print(f"✗ Frame stats test failed: {info.get('error')}")
    except Exception as e:
        print(f"✗ Frame stats test failed: {e}")
    
    # Test frame read
    try:
        results, stats = frame_read(section_slug="agbase", frame_slug="agbase_org__slim", nrows=5)
        if 'error' not in stats:
            print(f"✓ Frame read test passed - read {stats.get('total_results', 0)} records")
        else:
            print(f"✗ Frame read test failed: {stats.get('error')}")
    except Exception as e:
        print(f"✗ Frame read test failed: {e}")
    
    print("spr_agf_frames test complete.")
