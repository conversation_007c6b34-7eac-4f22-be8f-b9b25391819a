#!/usr/bin/env python3
"""
Test LLM Sprite Integration with MCP Adapter Pattern

This tests that the LLM sprite functions work correctly for CodeAgent integration.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent.parent))  # Add agbase_admin to path

from gaia.gaia_sprites.spr_llm_smart import (
    llm_completion_text, llm_completion_json, llm_analyze_code,
    llm_generate_code, llm_explain_concept, llm_debug_error
)

def test_llm_completion_text():
    """Test simple text completion"""
    print("=== Testing LLM Text Completion ===")
    
    result = llm_completion_text("What is Python? Answer in one sentence.")
    print(f"Result: {result}")
    
    if "python" in result.lower() and len(result) > 10:
        print("✅ Text completion working")
        return True
    else:
        print("❌ Text completion failed")
        return False

def test_llm_completion_json():
    """Test JSON completion"""
    print("\n=== Testing LLM JSON Completion ===")
    
    schema = {
        "type": "object",
        "properties": {
            "languages": {"type": "array", "items": {"type": "string"}},
            "count": {"type": "number"}
        }
    }
    
    result = llm_completion_json(
        "List 3 popular programming languages",
        schema=schema
    )
    print(f"Result: {result}")
    
    if isinstance(result, dict) and "data" in result:
        print("✅ JSON completion working")
        return True
    else:
        print("❌ JSON completion failed")
        return False

def test_llm_analyze_code():
    """Test code analysis"""
    print("\n=== Testing LLM Code Analysis ===")

    code = """
def calculate_area(radius):
    return 3.14 * radius * radius
"""

    print(f"🔍 Analyzing code:")
    print("```python")
    print(code.strip())
    print("```")

    result = llm_analyze_code(code, "python")
    print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

    # Show the actual analysis
    if isinstance(result, dict):
        if "analysis" in result:
            analysis = result["analysis"]
            print(f"\n📊 Analysis Results:")
            if isinstance(analysis, dict):
                for key, value in analysis.items():
                    print(f"  {key}: {value}")
            else:
                print(f"  {analysis}")
        elif "data" in result:
            print(f"\n📊 Analysis Data: {result['data']}")
        else:
            print(f"Full result: {result}")

    if isinstance(result, dict) and ("analysis" in result or "data" in result):
        print("✅ Code analysis working")
        return True
    else:
        print("❌ Code analysis failed")
        return False

def test_llm_generate_code():
    """Test code generation"""
    print("\n=== Testing LLM Code Generation ===")

    result = llm_generate_code("Create a function that adds two numbers", "python")
    print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

    # Show the actual generated code
    if isinstance(result, dict):
        if "data" in result and isinstance(result["data"], dict):
            code = result["data"].get("code", "")
            explanation = result["data"].get("explanation", "")
            usage = result["data"].get("usage_example", "")

            print(f"\n📝 Generated Code:")
            print("```python")
            print(code)
            print("```")

            if explanation:
                print(f"\n💡 Explanation: {explanation}")

            if usage:
                print(f"\n🔧 Usage Example:")
                print("```python")
                print(usage)
                print("```")
        else:
            print(f"Full result: {result}")

    if isinstance(result, dict) and ("data" in result or "code" in result):
        print("✅ Code generation working")
        return True
    else:
        print("❌ Code generation failed")
        return False

def test_llm_explain_concept():
    """Test concept explanation"""
    print("\n=== Testing LLM Concept Explanation ===")

    result = llm_explain_concept("recursion", "beginner")

    # Handle both string and dict responses
    explanation_text = ""
    if isinstance(result, str):
        explanation_text = result
        print(f"📖 Explanation: {result}")
    elif isinstance(result, dict):
        # Try to extract text from dict response
        explanation_text = (
            result.get("data", "") or
            result.get("text", "") or
            result.get("explanation", "") or
            str(result)
        )
        print(f"📖 Explanation: {explanation_text}")
    else:
        print(f"Unexpected result type: {type(result)}")
        print(f"Result: {result}")

    if explanation_text and "recursion" in explanation_text.lower() and len(explanation_text) > 20:
        print("✅ Concept explanation working")
        return True
    else:
        print("❌ Concept explanation failed")
        return False

def test_llm_debug_error():
    """Test error debugging"""
    print("\n=== Testing LLM Error Debugging ===")
    
    result = llm_debug_error("NameError: name 'x' is not defined", "print(x)")
    print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
    
    if isinstance(result, dict) and ("data" in result or "likely_cause" in result):
        print("✅ Error debugging working")
        return True
    else:
        print("❌ Error debugging failed")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing LLM Sprite Integration for CodeAgent")
    print("=" * 50)
    
    tests = [
        test_llm_completion_text,
        test_llm_completion_json,
        test_llm_analyze_code,
        test_llm_generate_code,
        test_llm_explain_concept,
        test_llm_debug_error
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All LLM sprite functions working perfectly!")
        print("✅ Ready for CodeAgent integration via mcpadapt")
    else:
        print("⚠️  Some tests failed - check individual results above")
    
    return passed == total

if __name__ == "__main__":
    main()
