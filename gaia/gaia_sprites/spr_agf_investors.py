# Simple 1-line interface for Investor Intelligence

def investor_search(name="<PERSON><PERSON><PERSON>", country="SG", investor_type="venture vc", min_investments=5, limit=100):
    """One-liner to search for investors with filters"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import duckdb
        
        # Get investor frame path
        frame_path = gaia_frames.get_frame_path(section_slug="cb_aug_join", frame_slug="inv_all_zsector")
        
        if not os.path.exists(frame_path):
            return [], {"error": f"Investor frame not found: {frame_path}", "total_results": 0}
        
        # Build where conditions
        where_conditions = []
        
        if name:
            where_conditions.append(f"lower(name) like lower('%{name}%')")
        
        if country:
            where_conditions.append(f"country_code='{country}'")
        
        if investor_type:
            type_vals = investor_type.split(' ')
            type_conditions = []
            for tv in type_vals:
                type_conditions.append(f"contains(investor_types, '{tv}')")
            where_conditions.append('(' + ' or '.join(type_conditions) + ')')
        
        if min_investments:
            where_conditions.append(f"count >= {min_investments}")
        
        # Build query
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        search_query = f"""
        SELECT *
        FROM '{frame_path}'
        WHERE {where_clause}
        ORDER BY sector_en DESC NULLS LAST
        LIMIT {limit}
        """
        
        # Execute query
        df = duckdb.query(search_query).df()
        
        # Convert to list of dictionaries
        results = df.to_dict('records')
        
        stats = {
            "total_results": len(results),
            "name": name,
            "country": country,
            "investor_type": investor_type,
            "min_investments": min_investments,
            "frame_path": frame_path
        }
        
        return results, stats
        
    except Exception as e:
        return [], {"error": f"Error in investor_search: {str(e)}", "total_results": 0}

def investor_stats(investor_name="Temasek Holdings", country="SG"):
    """Get detailed investor statistics and portfolio analysis"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import pandas as pd
        
        # Read investment data
        df = gaia_frames.read_frame_pandas(section_slug='cb_aug_join', frame_slug='invms_fr_invr_org', nrows=10000)
        
        # Read agbase companies for filtering
        agb = gaia_frames.read_frame_pandas(section_slug='agbase', frame_slug='agbase_org__slim', nrows=10000)
        
        # Filter for included companies only
        included_uuids = agb[agb.rec_inclusion == 'Y'].cb_uuid.tolist()
        df = df[df['org_uuid'].isin(included_uuids)]
        
        # Add year column
        df['year'] = pd.to_datetime(df['fr_announced_on']).dt.year
        
        # Filter for specific investor
        df_investor = df[df['invr_name'].astype(str).str.contains(investor_name, na=False)]
        
        if len(df_investor) == 0:
            return {}, {"error": f"No investments found for investor: {investor_name}", "total_investments": 0}
        
        # Add geo flag for home country analysis
        df_investor['org_geo_alpha_2'] = df_investor['org_geo_alpha_2'].fillna('')
        df_investor['geo_flag'] = (df_investor['org_geo_alpha_2'] == country).astype(int)
        
        # Calculate statistics
        total_investments = len(df_investor)
        total_amount_mm = df_investor['fr_raised_amount_usd_mm'].sum()
        avg_investment_mm = df_investor['fr_raised_amount_usd_mm'].mean()
        
        # Year-over-year analysis
        yearly_stats = df_investor.groupby('year').agg({
            'fr_raised_amount_usd_mm': ['sum', 'count', 'mean'],
            'geo_flag': 'mean'
        }).round(2)
        
        # Recent investments (last 2 years)
        recent_years = df_investor['year'].max() - 1
        recent_investments = df_investor[df_investor['year'] >= recent_years]
        
        # Top portfolio companies by investment amount
        top_companies = df_investor.groupby(['org_name', 'org_geo_alpha_2']).agg({
            'fr_raised_amount_usd_mm': 'sum',
            'fr_uuid': 'count'
        }).sort_values('fr_raised_amount_usd_mm', ascending=False).head(10)
        
        # Geographic distribution
        geo_distribution = df_investor.groupby('org_geo_alpha_2').agg({
            'fr_raised_amount_usd_mm': 'sum',
            'fr_uuid': 'count'
        }).sort_values('fr_raised_amount_usd_mm', ascending=False)
        
        stats = {
            "investor_name": investor_name,
            "home_country": country,
            "total_investments": int(total_investments),
            "total_amount_usd_mm": float(total_amount_mm),
            "avg_investment_usd_mm": float(avg_investment_mm),
            "investment_years": sorted(df_investor['year'].unique().tolist()),
            "home_country_ratio": float(df_investor['geo_flag'].mean()),
            "yearly_stats": yearly_stats.to_dict(),
            "recent_investments_count": len(recent_investments),
            "top_companies": top_companies.to_dict('index'),
            "geographic_distribution": geo_distribution.to_dict('index')
        }
        
        return stats, {"success": True, "total_investments": total_investments}
        
    except Exception as e:
        return {}, {"error": f"Error in investor_stats: {str(e)}", "total_investments": 0}

def coinvestor_analysis(investor1="Temasek", investor2="GIC", min_coinvestments=2):
    """Analyze co-investment patterns between two investors"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import pandas as pd
        
        # Read investment data
        df = gaia_frames.read_frame_pandas(section_slug='cb_aug_join', frame_slug='invms_fr_invr_org', nrows=20000)
        
        # Filter for both investors
        df1 = df[df['invr_name'].astype(str).str.contains(investor1, na=False)]
        df2 = df[df['invr_name'].astype(str).str.contains(investor2, na=False)]
        
        # Find common funding rounds (co-investments)
        common_rounds = set(df1['fr_uuid'].tolist()).intersection(set(df2['fr_uuid'].tolist()))
        
        if len(common_rounds) < min_coinvestments:
            return {}, {"error": f"Insufficient co-investments found (minimum: {min_coinvestments})", "coinvestments": len(common_rounds)}
        
        # Get details of co-investments
        coinvestments_df = df[df['fr_uuid'].isin(common_rounds)]
        
        # Group by funding round to get co-investment details
        coinvestment_details = coinvestments_df.groupby('fr_uuid').agg({
            'org_name': 'first',
            'org_geo_alpha_2': 'first',
            'fr_announced_on': 'first',
            'fr_raised_amount_usd_mm': 'first',
            'fr_investment_type': 'first',
            'invr_name': lambda x: list(x)
        }).reset_index()
        
        # Add year
        coinvestment_details['year'] = pd.to_datetime(coinvestment_details['fr_announced_on']).dt.year
        
        # Calculate statistics
        total_coinvestments = len(common_rounds)
        total_amount_mm = coinvestment_details['fr_raised_amount_usd_mm'].sum()
        avg_round_size_mm = coinvestment_details['fr_raised_amount_usd_mm'].mean()
        
        # Year distribution
        yearly_coinvestments = coinvestment_details.groupby('year').size().to_dict()
        
        # Geographic distribution
        geo_distribution = coinvestment_details.groupby('org_geo_alpha_2').size().to_dict()
        
        # Investment type distribution
        type_distribution = coinvestment_details.groupby('fr_investment_type').size().to_dict()
        
        stats = {
            "investor1": investor1,
            "investor2": investor2,
            "total_coinvestments": int(total_coinvestments),
            "total_amount_usd_mm": float(total_amount_mm),
            "avg_round_size_usd_mm": float(avg_round_size_mm),
            "coinvestment_years": sorted(coinvestment_details['year'].unique().tolist()),
            "yearly_distribution": yearly_coinvestments,
            "geographic_distribution": geo_distribution,
            "investment_type_distribution": type_distribution,
            "recent_coinvestments": coinvestment_details.sort_values('fr_announced_on', ascending=False).head(5).to_dict('records')
        }
        
        return stats, {"success": True, "coinvestments": total_coinvestments}
        
    except Exception as e:
        return {}, {"error": f"Error in coinvestor_analysis: {str(e)}", "coinvestments": 0}

def investor_rankings(year=2023, limit=20):
    """Get top investor rankings for a specific year"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import pandas as pd
        
        # Read investment data
        df = gaia_frames.read_frame_pandas(section_slug='cb_aug_join', frame_slug='invms_fr_invr_org', nrows=50000)
        
        # Add year column and filter
        df['year'] = pd.to_datetime(df['fr_announced_on']).dt.year
        df_year = df[df['year'] == year]
        
        if len(df_year) == 0:
            return [], {"error": f"No investment data found for year: {year}", "total_investors": 0}
        
        # Group by investor and calculate metrics
        investor_rankings = df_year.groupby(['invr_uuid', 'invr_name']).agg({
            'fr_raised_amount_usd_mm': ['sum', 'mean', 'count'],
            'org_uuid': 'nunique'  # unique companies invested in
        }).round(2)
        
        # Flatten column names
        investor_rankings.columns = ['total_amount_mm', 'avg_amount_mm', 'total_rounds', 'unique_companies']
        
        # Sort by total amount and add ranking
        investor_rankings = investor_rankings.sort_values('total_amount_mm', ascending=False).reset_index()
        investor_rankings['rank'] = range(1, len(investor_rankings) + 1)
        
        # Limit results
        top_investors = investor_rankings.head(limit)
        
        # Convert to list of dictionaries
        results = top_investors.to_dict('records')
        
        stats = {
            "year": year,
            "total_investors": len(investor_rankings),
            "top_investors_shown": len(results),
            "total_investment_amount_mm": float(df_year['fr_raised_amount_usd_mm'].sum()),
            "total_rounds": len(df_year)
        }
        
        return results, stats
        
    except Exception as e:
        return [], {"error": f"Error in investor_rankings: {str(e)}", "total_investors": 0}

def investor_portfolio(investor_name="Sequoia", sector_filter=None, limit=50):
    """Get investor's portfolio companies with optional sector filtering"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_frames import gaia_frames
        import pandas as pd
        
        # Read investment data
        df = gaia_frames.read_frame_pandas(section_slug='cb_aug_join', frame_slug='invms_fr_invr_org', nrows=20000)
        
        # Filter for specific investor
        df_investor = df[df['invr_name'].astype(str).str.contains(investor_name, na=False)]
        
        if len(df_investor) == 0:
            return [], {"error": f"No investments found for investor: {investor_name}", "total_companies": 0}
        
        # Group by company to get portfolio
        portfolio = df_investor.groupby(['org_uuid', 'org_name', 'org_geo_alpha_2']).agg({
            'fr_raised_amount_usd_mm': ['sum', 'count'],
            'fr_announced_on': ['min', 'max'],
            'fr_investment_type': lambda x: list(set(x))
        }).round(2)
        
        # Flatten column names
        portfolio.columns = ['total_invested_mm', 'rounds_count', 'first_investment', 'last_investment', 'investment_types']
        
        # Add investment duration in days
        portfolio['investment_duration_days'] = (
            pd.to_datetime(portfolio['last_investment']) - 
            pd.to_datetime(portfolio['first_investment'])
        ).dt.days
        
        # Sort by total invested amount
        portfolio = portfolio.sort_values('total_invested_mm', ascending=False).reset_index()
        
        # Apply sector filter if provided
        if sector_filter:
            # This would require sector data - for now, just return all
            pass
        
        # Limit results
        portfolio_limited = portfolio.head(limit)
        
        # Convert to list of dictionaries
        results = portfolio_limited.to_dict('records')
        
        stats = {
            "investor_name": investor_name,
            "total_companies": len(portfolio),
            "companies_shown": len(results),
            "total_invested_mm": float(portfolio['total_invested_mm'].sum()),
            "avg_investment_per_company_mm": float(portfolio['total_invested_mm'].mean()),
            "sector_filter": sector_filter
        }
        
        return results, stats
        
    except Exception as e:
        return [], {"error": f"Error in investor_portfolio: {str(e)}", "total_companies": 0}

# Usage examples:
# results, stats = investor_search("Temasek", country="SG", investor_type="venture vc")
# stats, info = investor_stats("Temasek Holdings", country="SG")
# analysis, info = coinvestor_analysis("Temasek", "GIC")
# rankings, stats = investor_rankings(year=2023, limit=20)
# portfolio, stats = investor_portfolio("Sequoia", limit=30)

if __name__ == "__main__":
    """Test the investors sprite when run directly"""
    print("Testing spr_agf_investors sprite...")
    
    # Test investor search
    try:
        results, stats = investor_search("Sequoia", country="US", limit=5)
        if 'error' not in stats:
            print(f"✓ Investor search test passed - found {stats.get('total_results', 0)} investors")
        else:
            print(f"✗ Investor search test failed: {stats.get('error')}")
    except Exception as e:
        print(f"✗ Investor search test failed: {e}")
    
    # Test investor rankings
    try:
        rankings, stats = investor_rankings(year=2023, limit=5)
        if 'error' not in stats:
            print(f"✓ Investor rankings test passed - found {stats.get('total_investors', 0)} investors for 2023")
        else:
            print(f"✗ Investor rankings test failed: {stats.get('error')}")
    except Exception as e:
        print(f"✗ Investor rankings test failed: {e}")
    
    # Test investor portfolio
    try:
        portfolio, stats = investor_portfolio("Sequoia", limit=3)
        if 'error' not in stats:
            print(f"✓ Investor portfolio test passed - found {stats.get('total_companies', 0)} portfolio companies")
        else:
            print(f"✗ Investor portfolio test failed: {stats.get('error')}")
    except Exception as e:
        print(f"✗ Investor portfolio test failed: {e}")
    
    print("spr_agf_investors test complete.")
