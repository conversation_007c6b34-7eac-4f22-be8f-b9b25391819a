#!/usr/bin/env python3
"""
Test Deep Research Sprite Integration

Tests the deep research sprite functionality for CodeAgent integration.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent.parent))  # Add agbase_admin to path

from gaia.gaia_sprites.spr_ext_deep_research import (
    deep_research_simple, deep_research_robust, deep_research_batch,
    deep_research_load, deep_research_list, DEEP_RESEARCH_AVAILABLE
)

def test_availability():
    """Test if deep research functionality is available"""
    print("=== Testing Deep Research Availability ===")
    
    if DEEP_RESEARCH_AVAILABLE:
        print("✅ Deep research functionality is available")
        return True
    else:
        print("❌ Deep research functionality is not available")
        print("   This may be due to missing dependencies or environment setup")
        return False

def test_simple_research():
    """Test simple deep research functionality"""
    print("\n=== Testing Simple Deep Research ===")
    
    if not DEEP_RESEARCH_AVAILABLE:
        print("⏭️  Skipping - deep research not available")
        return False
    
    try:
        # Test with a simple query
        result = deep_research_simple(
            query="What are the key benefits of renewable energy?",
            save_slug="test_renewable_energy",
            project="test_project"
        )
        
        print(f"📊 Result keys: {list(result.keys())}")
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        print(f"📝 Content length: {len(result.get('content', ''))} characters")
        print(f"🔧 Metadata: {result.get('metadata', {})}")
        
        # Check if research was saved
        if result.get('metadata', {}).get('saved_to'):
            print(f"💾 Saved to: {result['metadata']['saved_to']}")
        
        print("✅ Simple research test completed")
        return True
        
    except Exception as e:
        print(f"❌ Simple research test failed: {e}")
        return False

def test_research_list():
    """Test research listing functionality"""
    print("\n=== Testing Research List ===")
    
    if not DEEP_RESEARCH_AVAILABLE:
        print("⏭️  Skipping - deep research not available")
        return False
    
    try:
        result = deep_research_list(project="test_project")
        
        print(f"📊 Result keys: {list(result.keys())}")
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        research_list = result.get('research_list', [])
        print(f"📋 Found {len(research_list)} research items")
        
        if research_list:
            print("📄 Research items:")
            for item in research_list[:3]:  # Show first 3 items
                print(f"   • {item.get('slug', 'Unknown')} - {item.get('timestamp', 'No timestamp')}")
        
        print("✅ Research list test completed")
        return True
        
    except Exception as e:
        print(f"❌ Research list test failed: {e}")
        return False

def test_batch_research():
    """Test batch research functionality"""
    print("\n=== Testing Batch Deep Research ===")
    
    if not DEEP_RESEARCH_AVAILABLE:
        print("⏭️  Skipping - deep research not available")
        return False
    
    try:
        # Test with multiple small queries
        queries = {
            "solar_power": "What are the latest developments in solar power efficiency?",
            "wind_energy": "What are the current challenges in wind energy deployment?"
        }
        
        result = deep_research_batch(
            queries=queries,
            project="test_batch",
            parallel=True
        )
        
        print(f"📊 Batch result keys: {list(result.keys())}")
        
        success_count = 0
        for slug, res in result.items():
            if "error" not in res:
                success_count += 1
                print(f"✅ {slug}: {len(res.get('content', ''))} characters")
            else:
                print(f"❌ {slug}: {res['error']}")
        
        print(f"📈 Batch success rate: {success_count}/{len(queries)}")
        
        if success_count > 0:
            print("✅ Batch research test completed")
            return True
        else:
            print("❌ No batch research succeeded")
            return False
        
    except Exception as e:
        print(f"❌ Batch research test failed: {e}")
        return False

def test_robust_research():
    """Test robust deep research with evaluation"""
    print("\n=== Testing Robust Deep Research ===")
    
    if not DEEP_RESEARCH_AVAILABLE:
        print("⏭️  Skipping - deep research not available")
        return False
    
    try:
        # Test with a research query that needs evaluation
        result = deep_research_robust(
            query="Analyze the economic impact of electric vehicle adoption",
            criteria="Comprehensive analysis with data, sources, and clear conclusions",
            quality_level="low",  # Use low for faster testing
            execution_mode="sequential"  # Sequential for simpler testing
        )
        
        print(f"📊 Robust result keys: {list(result.keys())}")
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        if result.get('success'):
            evaluation = result.get('evaluation', {})
            print(f"📈 Success: {result['success']}")
            print(f"📝 Content length: {len(result.get('content', ''))} characters")
            print(f"🎯 Average score: {evaluation.get('avg_score', 0):.1f}/10")
            print(f"👥 Passing critics: {evaluation.get('passing_critics', 0)}/{evaluation.get('total_critics', 0)}")
            print(f"🤝 Consensus: {'✅ Yes' if evaluation.get('consensus_reached') else '❌ No'}")
            
            print("✅ Robust research test completed")
            return True
        else:
            print(f"❌ Robust research failed: {result.get('error', 'Unknown error')}")
            return False
        
    except Exception as e:
        print(f"❌ Robust research test failed: {e}")
        return False

def main():
    """Run all deep research tests"""
    print("🔬 Testing Deep Research Sprite Integration")
    print("=" * 60)
    
    tests = [
        ("Availability Check", test_availability),
        ("Simple Research", test_simple_research),
        ("Research List", test_research_list),
        ("Batch Research", test_batch_research),
        ("Robust Research", test_robust_research)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All deep research functionality working!")
        print("✅ Ready for CodeAgent integration via mcpadapt")
    elif passed > 0:
        print("⚠️  Some functionality working - check individual results")
        print("✅ Partial integration ready for CodeAgent")
    else:
        print("❌ Deep research functionality not working")
        print("🔧 Check environment setup and dependencies")
    
    return passed == total

if __name__ == "__main__":
    main()
