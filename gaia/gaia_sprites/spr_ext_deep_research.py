#!/usr/bin/env python3
"""
External Deep Research Sprite

Provides comprehensive deep research capabilities using OpenAI's deep research models
with web search, code interpretation, and robust evaluation systems.

Features:
- Simple deep research with caching
- Robust deep research with multi-critic evaluation
- Batch processing capabilities
- Configurable quality levels (low/medium/high)
- Storage and retrieval of research results
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import json

# Add parent directories to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))  # Add gaia to path
sys.path.insert(0, str(current_dir.parent.parent))  # Add agbase_admin to path

try:
    from gaia.gaia_elf.deep_research.deep_research_oai_lib import (
        research, research_batch, configure_deep_research, 
        load_research, list_research
    )
    from gaia.gaia_elf.deep_research.robust.core import RobustProcessor
    from gaia.gaia_elf.deep_research.robust.config import LOW, MED, HIGH
    DEEP_RESEARCH_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Deep research modules not available: {e}")
    DEEP_RESEARCH_AVAILABLE = False


def deep_research_simple(
    query: str,
    model: str = "o4-mini-deep-research",
    save_slug: Optional[str] = None,
    project: str = "general",
    use_cache: bool = True
) -> Dict[str, Any]:
    """
    Perform simple deep research on a query
    
    Args:
        query: Research question or topic
        model: Model to use for research
        save_slug: Optional slug for saving results
        project: Project name for organization
        use_cache: Whether to use caching
        
    Returns:
        Research results with content, metadata, and sources
    """
    if not DEEP_RESEARCH_AVAILABLE:
        return {
            "error": "Deep research functionality not available",
            "content": "",
            "metadata": {}
        }
    
    try:
        # Configure deep research if not already done
        configure_deep_research(
            storage_folder="/var/lib/gaia/GAIA_FS/deep_research/storage/",
            cache_folder="/var/lib/gaia/GAIA_FS/deep_research/cache/",
            no_cache=not use_cache
        )
        
        # Perform research
        result = research(
            input_text=query,
            slug=save_slug,
            project=project,
            model=model,
            tools=[
                {"type": "web_search_preview"},
                {"type": "code_interpreter", "container": {"type": "auto"}},
            ],
            background=True,
            verbose=False
        )
        
        # Extract content from result
        content = ""
        metadata = {}
        
        if result and isinstance(result, dict):
            if "out_short" in result:
                out_short = result["out_short"]
                if out_short.get("status") == "completed":
                    content = out_short.get("output_text", "")
                    metadata = {
                        "model": model,
                        "status": out_short.get("status"),
                        "usage": out_short.get("usage", {}),
                        "tools_used": ["web_search", "code_interpreter"]
                    }
            
            # Add save information if available
            if "_saved_to" in result:
                metadata["saved_to"] = result["_saved_to"]
        
        return {
            "content": content,
            "metadata": metadata,
            "raw_result": result
        }
        
    except Exception as e:
        return {
            "error": str(e),
            "content": "",
            "metadata": {}
        }


def deep_research_robust(
    query: str,
    criteria: str = "Comprehensive, accurate, well-sourced research with clear analysis",
    quality_level: str = "medium",
    execution_mode: str = "parallel"
) -> Dict[str, Any]:
    """
    Perform robust deep research with multi-critic evaluation
    
    Args:
        query: Research question or topic
        criteria: Evaluation criteria for critics
        quality_level: Quality level - "low", "medium", or "high"
        execution_mode: "sequential" or "parallel" critic evaluation
        
    Returns:
        Research results with evaluation scores and consensus information
    """
    if not DEEP_RESEARCH_AVAILABLE:
        return {
            "error": "Deep research functionality not available",
            "success": False,
            "content": "",
            "evaluation": {}
        }
    
    try:
        # Select configuration based on quality level
        config_map = {
            "low": LOW,
            "medium": MED,
            "high": HIGH
        }
        
        config = config_map.get(quality_level.lower(), MED).copy()
        config["execution"] = execution_mode
        
        # Run robust processing
        processor = RobustProcessor(config)
        result = processor.run("deep_research", query, criteria)
        
        return result
        
    except Exception as e:
        return {
            "error": str(e),
            "success": False,
            "content": "",
            "evaluation": {}
        }


def deep_research_batch(
    queries: Dict[str, str],
    model: str = "o4-mini-deep-research",
    project: str = "batch_research",
    parallel: bool = True,
    save_individual: bool = True
) -> Dict[str, Dict[str, Any]]:
    """
    Perform batch deep research on multiple queries
    
    Args:
        queries: Dictionary mapping slugs to research queries
        model: Model to use for research
        project: Project name for organization
        parallel: Whether to process in parallel
        save_individual: Whether to save each result individually
        
    Returns:
        Dictionary mapping slugs to research results
    """
    if not DEEP_RESEARCH_AVAILABLE:
        return {
            slug: {
                "error": "Deep research functionality not available",
                "content": "",
                "metadata": {}
            }
            for slug in queries.keys()
        }
    
    try:
        # Configure deep research
        configure_deep_research(
            storage_folder="/var/lib/gaia/GAIA_FS/deep_research/storage/",
            cache_folder="/var/lib/gaia/GAIA_FS/deep_research/cache/"
        )
        
        # Perform batch research
        results = research_batch(
            input_dict=queries,
            mode="parallel" if parallel else "sequential",
            project=project,
            save_individual=save_individual,
            model=model,
            tools=[
                {"type": "web_search_preview"},
                {"type": "code_interpreter", "container": {"type": "auto"}},
            ]
        )
        
        # Process results to extract content
        processed_results = {}
        for slug, result in results.items():
            content = ""
            metadata = {}
            
            if result and isinstance(result, dict):
                if "out_short" in result:
                    out_short = result["out_short"]
                    if out_short.get("status") == "completed":
                        content = out_short.get("output_text", "")
                        metadata = {
                            "model": model,
                            "status": out_short.get("status"),
                            "usage": out_short.get("usage", {}),
                            "tools_used": ["web_search", "code_interpreter"]
                        }
                
                if "_saved_to" in result:
                    metadata["saved_to"] = result["_saved_to"]
            
            processed_results[slug] = {
                "content": content,
                "metadata": metadata,
                "raw_result": result
            }
        
        return processed_results
        
    except Exception as e:
        return {
            slug: {
                "error": str(e),
                "content": "",
                "metadata": {}
            }
            for slug in queries.keys()
        }


def deep_research_load(slug: str, project: str = "general") -> Dict[str, Any]:
    """
    Load previously saved research results
    
    Args:
        slug: Research slug to load
        project: Project name
        
    Returns:
        Loaded research data or error information
    """
    if not DEEP_RESEARCH_AVAILABLE:
        return {
            "error": "Deep research functionality not available",
            "content": "",
            "metadata": {}
        }
    
    try:
        # Configure storage
        configure_deep_research(
            storage_folder="/var/lib/gaia/GAIA_FS/deep_research/storage/"
        )
        
        result = load_research(slug, project)
        
        if result is None:
            return {
                "error": f"Research not found: {slug} in project {project}",
                "content": "",
                "metadata": {}
            }
        
        return {
            "content": result.get("content", ""),
            "metadata": result.get("metadata", {}),
            "raw_result": result
        }
        
    except Exception as e:
        return {
            "error": str(e),
            "content": "",
            "metadata": {}
        }


def deep_research_list(project: str = "general") -> Dict[str, Any]:
    """
    List all saved research in a project
    
    Args:
        project: Project name to list
        
    Returns:
        List of research metadata or error information
    """
    if not DEEP_RESEARCH_AVAILABLE:
        return {
            "error": "Deep research functionality not available",
            "research_list": []
        }
    
    try:
        # Configure storage
        configure_deep_research(
            storage_folder="/var/lib/gaia/GAIA_FS/deep_research/storage/"
        )
        
        research_list = list_research(project)
        
        return {
            "research_list": research_list,
            "project": project,
            "count": len(research_list)
        }
        
    except Exception as e:
        return {
            "error": str(e),
            "research_list": []
        }


# Demo function
def demo_deep_research():
    """Demo the deep research sprite capabilities"""
    print("=== Deep Research Sprite Demo ===")
    
    if not DEEP_RESEARCH_AVAILABLE:
        print("❌ Deep research functionality not available")
        return
    
    # Test simple research
    print("\n1. Simple Deep Research:")
    result = deep_research_simple(
        "What are the latest developments in quantum computing?",
        save_slug="quantum_demo"
    )
    
    if "error" in result:
        print(f"Error: {result['error']}")
    else:
        print(f"Content length: {len(result['content'])} characters")
        print(f"Metadata: {result['metadata']}")
    
    # Test listing research
    print("\n2. List Research:")
    research_list = deep_research_list()
    print(f"Found {research_list.get('count', 0)} research items")


if __name__ == "__main__":
    demo_deep_research()
