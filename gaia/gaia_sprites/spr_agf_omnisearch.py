# Simple 1-line interface for Omni Search

def omni_search(query="deepnote", query_type="Query String", limit=50):
    """One-liner to perform omni search across multiple search types"""
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        # Import search utilities
        from gaia.djangaia.health.search_utils import query_elasticsearch, knn_query, es_more_like_this, connect_opensearch
        
        # Initialize connection
        elastico, ES_INDEX = connect_opensearch(cluster='agsearch')
        
        results = []
        query_info = {}
        
        if query_type == "Query String":
            # Standard text search
            results, query_info = query_elasticsearch(
                query=query,
                cluster='agsearch',
                limit=limit
            )
            
        elif query_type == "Semantic Query":
            # Semantic/KNN search
            results, query_info = knn_query(
                query_text=query,
                elastico=elastico,
                idx=ES_INDEX,
                limit=limit
            )
            
        elif query_type == "More Like Vocabulary":
            # More like this search (requires like_ids)
            # For demo purposes, we'll do a basic search if no like_ids provided
            if query.startswith('G0__'):
                # If query looks like a GOID, use it as like_id
                results, query_info = es_more_like_this(
                    like_ids=[query],
                    elastico=elastico,
                    idx=ES_INDEX,
                    limit=limit
                )
            else:
                # Fall back to query string search
                results, query_info = query_elasticsearch(
                    query=query,
                    cluster='agsearch',
                    limit=limit
                )
        
        # Format results consistently
        formatted_results = []
        for result in results:
            formatted_result = {
                'goid': result.get('GOID', ''),
                'name': result.get('name', ''),
                'description': result.get('description', ''),
                'short_description': result.get('short_description', ''),
                'llm_description': result.get('llm_description', ''),
                'keywords': result.get('keywords', ''),
                'homepage_url': result.get('homepage_url', ''),
                'total_funding_usd_mm': result.get('total_funding_usd_mm', ''),
                'domain': result.get('dom', ''),
                'source_created_ts': result.get('source_created_ts', ''),
                'gaia_added_ts': result.get('gaia_added_ts', '')
            }
            formatted_results.append(formatted_result)
        
        stats = {
            'total_results': len(formatted_results),
            'query_type': query_type,
            'query': query,
            'limit': limit
        }
        
        return formatted_results, stats
        
    except Exception as e:
        print(f"Error in omni_search: {e}")
        # Return empty results for testing when dependencies are missing
        return [], {"total_results": 0, "error": str(e)}

def query_string_search(query="deepnote", limit=50):
    """Search using query string (keyword search)"""
    return omni_search(query=query, query_type="Query String", limit=limit)

def semantic_search(query="AI agriculture", limit=50):
    """Search using semantic/vector similarity"""
    return omni_search(query=query, query_type="Semantic Query", limit=limit)

def more_like_search(goid_or_query="deepnote", limit=50):
    """Find records similar to the given GOID or query"""
    return omni_search(query=goid_or_query, query_type="More Like Vocabulary", limit=limit)

def company_search(company_name="deepnote", limit=20):
    """Search specifically for companies by name"""
    return query_string_search(query=f'name:"{company_name}" OR "{company_name}"', limit=limit)

def funding_search(query="AI", min_funding_mm=1, limit=50):
    """Search for companies with minimum funding"""
    search_query = f"{query} AND total_funding_usd_mm:>={min_funding_mm}"
    return query_string_search(query=search_query, limit=limit)

def keyword_search(keywords="machine learning agriculture", limit=50):
    """Search by keywords"""
    return query_string_search(query=f'keywords:"{keywords}" OR "{keywords}"', limit=limit)

# Usage examples:
# results, stats = omni_search("deepnote")
# results, stats = semantic_search("AI agriculture startups")
# results, stats = company_search("deepnote")
# results, stats = funding_search("fintech", min_funding_mm=10)
# results, stats = keyword_search("machine learning agriculture")
# results, stats = more_like_search("G0__org__cbuuid__12345")

if __name__ == "__main__":
    """Test the omnisearch sprite when run directly"""
    print("Testing spr_omnisearch sprite...")
    
    # Test basic query string search
    try:
        results, stats = query_string_search("deepnote", limit=5)
        print(f"✓ Query string search test passed - found {stats.get('total_results', 0)} results")
        if results:
            print(f"Sample result: {results[0].get('name', 'N/A')}")
    except Exception as e:
        print(f"✗ Query string search test failed: {e}")
    
    # Test semantic search
    try:
        results, stats = semantic_search("AI agriculture", limit=3)
        print(f"✓ Semantic search test passed - found {stats.get('total_results', 0)} results")
    except Exception as e:
        print(f"✗ Semantic search test failed: {e}")
    
    # Test company search
    try:
        results, stats = company_search("deepnote", limit=3)
        print(f"✓ Company search test passed - found {stats.get('total_results', 0)} results")
    except Exception as e:
        print(f"✗ Company search test failed: {e}")
    
    print("spr_omnisearch test complete.")
