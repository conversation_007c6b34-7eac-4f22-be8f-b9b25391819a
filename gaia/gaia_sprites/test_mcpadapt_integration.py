#!/usr/bin/env python3
"""
Test MCPAdapt Integration with Sprite CodeAgent

This test verifies that the MCPAdapt + SmolAgentsAdapter integration works
correctly with our Gaia Sprite MCP server.
"""

def test_mcpadapt_imports():
    """Test that all required MCPAdapt components can be imported"""
    print("🧪 Testing MCPAdapt imports...")
    
    try:
        from mcpadapt.core import MCPAdapt
        print("✓ MCPAdapt core imported successfully")
    except ImportError as e:
        print(f"✗ MCPAdapt core import failed: {e}")
        return False
    
    try:
        from mcpadapt.smolagents_adapter import SmolAgentsAdapter
        print("✓ SmolAgentsAdapter imported successfully")
    except ImportError as e:
        print(f"✗ SmolAgentsAdapter import failed: {e}")
        return False
    
    try:
        from mcp import StdioServerParameters
        print("✓ MCP StdioServerParameters imported successfully")
    except ImportError as e:
        print(f"✗ MCP import failed: {e}")
        return False
    
    return True

def test_smolagents_imports():
    """Test that smolagents components can be imported"""
    print("\n🧪 Testing smolagents imports...")
    
    try:
        from smolagents import CodeAgent
        print("✓ CodeAgent imported successfully")
    except ImportError as e:
        print(f"✗ CodeAgent import failed: {e}")
        return False
    
    # Test model imports (at least one should work)
    model_imported = False
    
    try:
        from smolagents import LiteLLMModel
        print("✓ LiteLLMModel available")
        model_imported = True
    except ImportError:
        print("- LiteLLMModel not available")
    
    try:
        from smolagents import InferenceClientModel
        print("✓ InferenceClientModel available")
        model_imported = True
    except ImportError:
        print("- InferenceClientModel not available")
    
    try:
        from smolagents import TransformersModel
        print("✓ TransformersModel available")
        model_imported = True
    except ImportError:
        print("- TransformersModel not available")
    
    if not model_imported:
        print("✗ No smolagents model backends available")
        return False
    
    return True

def test_mcp_server_path():
    """Test that the MCP server file exists"""
    print("\n🧪 Testing MCP server path...")
    
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    mcp_server_path = os.path.join(current_dir, "gaia_sprite_mcp", "sprite_mcp_server.py")
    
    if os.path.exists(mcp_server_path):
        print(f"✓ MCP server found at: {mcp_server_path}")
        return True
    else:
        print(f"✗ MCP server not found at: {mcp_server_path}")
        return False

def test_mcpadapt_basic_usage():
    """Test basic MCPAdapt usage pattern"""
    print("\n🧪 Testing MCPAdapt basic usage...")
    
    try:
        from mcpadapt.core import MCPAdapt
        from mcpadapt.smolagents_adapter import SmolAgentsAdapter
        from mcp import StdioServerParameters
        import os
        
        # Get MCP server path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        mcp_server_path = os.path.join(current_dir, "gaia_sprite_mcp", "sprite_mcp_server.py")
        
        if not os.path.exists(mcp_server_path):
            print("✗ MCP server not found, skipping usage test")
            return False
        
        # Create server parameters
        server_params = StdioServerParameters(
            command="python3",
            args=[mcp_server_path]
        )
        
        print("✓ Server parameters created successfully")
        print("✓ MCPAdapt basic usage pattern validated")
        
        # Note: We don't actually start the server in this test
        # as it would require the full sprite environment
        
        return True
        
    except Exception as e:
        print(f"✗ MCPAdapt usage test failed: {e}")
        return False

def test_sprite_codeagent_structure():
    """Test that the sprite code agent has the right structure"""
    print("\n🧪 Testing sprite code agent structure...")
    
    try:
        from sprite_codeagent import create_sprite_codeagent, run_sprite_algorithm, USAGE_EXAMPLES
        print("✓ All main functions imported successfully")
        
        # Check that usage examples mention MCPAdapt
        if "MCPAdapt" in USAGE_EXAMPLES:
            print("✓ Usage examples mention MCPAdapt")
        else:
            print("- Usage examples don't mention MCPAdapt (may be okay)")
        
        return True
        
    except Exception as e:
        print(f"✗ Sprite code agent structure test failed: {e}")
        return False

def run_all_tests():
    """Run all integration tests"""
    print("🚀 MCPAdapt Integration Tests")
    print("=" * 50)
    
    tests = [
        ("MCPAdapt Imports", test_mcpadapt_imports),
        ("Smolagents Imports", test_smolagents_imports),
        ("MCP Server Path", test_mcp_server_path),
        ("MCPAdapt Basic Usage", test_mcpadapt_basic_usage),
        ("Sprite CodeAgent Structure", test_sprite_codeagent_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! MCPAdapt integration is ready.")
    else:
        print("⚠️  Some tests failed. Check dependencies and setup.")
        print("\nTo fix issues:")
        print("1. Install: pip install smolagents[mcp]")
        print("2. Ensure MCP server exists in gaia_sprite_mcp/")
        print("3. Check that all sprite dependencies are available")
    
    return passed == len(results)

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
