#!/usr/bin/env python3
"""
Smart LLM Sprite for CodeAgent

Provides intelligent LLM capabilities through MCP adapter pattern.
Supports both simple completions and structured JSON responses.
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
import json

# Add parent directories to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))  # Add gaia to path
sys.path.insert(0, str(current_dir.parent.parent))  # Add agbase_admin to path

from gaia.gaia_llm.gaia_llm import LiteLLMJsonChatCompletionClient


class LLMSmartSprite:
    """Smart LLM sprite providing various LLM capabilities"""
    
    def __init__(self, model: str = "anthropic/claude-3-5-sonnet-20241022", max_tokens: int = 4000):
        """Initialize the LLM sprite"""
        self.model = model
        self.max_tokens = max_tokens
        self.client = LiteLLMJsonChatCompletionClient(
            model=self.model,
            max_tokens=self.max_tokens
        )
        
    def completion_text(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """
        Simple text completion

        Args:
            prompt: User prompt
            system_prompt: Optional system prompt

        Returns:
            Text response from LLM
        """
        try:
            # Combine system prompt with user prompt if provided
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n{prompt}"

            # Use the JSON client but extract text response
            result = self.client.completion_json(
                user_prompt=full_prompt,
                verbose=False
            )

            # Try different response keys and handle nested data
            text_response = ""

            if "data" in result:
                data = result["data"]
                if isinstance(data, dict):
                    # Try common text keys in nested data
                    text_response = (
                        data.get("text", "") or
                        data.get("content", "") or
                        data.get("explanation", "") or
                        data.get("response", "") or
                        str(data)
                    )
                else:
                    text_response = str(data)
            else:
                text_response = (
                    result.get("text", "") or
                    result.get("content", "") or
                    result.get("response", "") or
                    str(result)
                )

            return text_response.strip() if hasattr(text_response, 'strip') else str(text_response)

        except Exception as e:
            return f"Error: {str(e)}"
    
    def completion_json(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        schema: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Structured JSON completion

        Args:
            prompt: User prompt
            system_prompt: Optional system prompt
            schema: Optional JSON schema for structured output

        Returns:
            JSON response from LLM
        """
        try:
            # Combine system prompt with user prompt if provided
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n{prompt}"

            # Add schema to prompt if provided
            if schema:
                schema_text = json.dumps(schema, indent=2)
                full_prompt = f"{full_prompt}\n\nPlease respond with JSON matching this schema:\n{schema_text}"

            result = self.client.completion_json(
                user_prompt=full_prompt,
                verbose=False
            )

            return result

        except Exception as e:
            return {"error": str(e)}
    
    def analyze_code(self, code: str, language: str = "python") -> Dict[str, Any]:
        """
        Analyze code and provide insights
        
        Args:
            code: Code to analyze
            language: Programming language
            
        Returns:
            Analysis results
        """
        prompt = f"""
Analyze this {language} code and provide insights:

```{language}
{code}
```

Please analyze:
1. Code quality and style
2. Potential issues or bugs
3. Suggestions for improvement
4. Security considerations
5. Performance implications

Return as JSON with keys: quality_score, issues, suggestions, security_notes, performance_notes
"""
        
        return self.completion_json(prompt)
    
    def generate_code(
        self, 
        description: str, 
        language: str = "python",
        style: str = "clean"
    ) -> Dict[str, Any]:
        """
        Generate code based on description
        
        Args:
            description: What the code should do
            language: Programming language
            style: Code style (clean, minimal, verbose)
            
        Returns:
            Generated code and explanation
        """
        prompt = f"""
Generate {language} code that {description}.

Requirements:
- Use {style} coding style
- Include proper error handling
- Add helpful comments
- Follow best practices

Return as JSON with keys: code, explanation, usage_example
"""
        
        return self.completion_json(prompt)
    
    def explain_concept(self, concept: str, level: str = "intermediate") -> str:
        """
        Explain a programming or technical concept
        
        Args:
            concept: Concept to explain
            level: Explanation level (beginner, intermediate, advanced)
            
        Returns:
            Explanation text
        """
        prompt = f"""
Explain the concept of "{concept}" at a {level} level.

Provide:
1. Clear definition
2. Key characteristics
3. Common use cases
4. Simple examples if applicable
5. Related concepts

Make it accessible for someone at the {level} level.
"""
        
        return self.completion_text(prompt)
    
    def debug_error(self, error_message: str, code_context: str = "") -> Dict[str, Any]:
        """
        Help debug an error
        
        Args:
            error_message: The error message
            code_context: Optional code context where error occurred
            
        Returns:
            Debug analysis and solutions
        """
        prompt = f"""
Help debug this error:

Error: {error_message}

Code context:
{code_context}

Please provide:
1. Likely cause of the error
2. Step-by-step debugging approach
3. Potential solutions
4. Prevention strategies

Return as JSON with keys: likely_cause, debugging_steps, solutions, prevention
"""
        
        return self.completion_json(prompt)
    
    def review_pull_request(self, diff: str, description: str = "") -> Dict[str, Any]:
        """
        Review a pull request or code diff
        
        Args:
            diff: Git diff or code changes
            description: Optional PR description
            
        Returns:
            Review feedback
        """
        prompt = f"""
Review this code change:

Description: {description}

Diff:
{diff}

Please provide:
1. Overall assessment
2. Specific feedback on changes
3. Potential issues or concerns
4. Suggestions for improvement
5. Approval recommendation

Return as JSON with keys: assessment, feedback, issues, suggestions, recommendation
"""
        
        return self.completion_json(prompt)
    
    def optimize_query(self, query: str, context: str = "") -> Dict[str, Any]:
        """
        Optimize a database query or search
        
        Args:
            query: Query to optimize
            context: Optional context about the data/system
            
        Returns:
            Optimization suggestions
        """
        prompt = f"""
Optimize this query:

Query: {query}

Context: {context}

Please provide:
1. Analysis of current query
2. Performance bottlenecks
3. Optimized version
4. Explanation of improvements
5. Additional recommendations

Return as JSON with keys: analysis, bottlenecks, optimized_query, improvements, recommendations
"""
        
        return self.completion_json(prompt)


# MCP Adapter Functions for CodeAgent
def llm_completion_text(prompt: str, system_prompt: str = None, model: str = None) -> str:
    """Simple text completion for CodeAgent"""
    sprite = LLMSmartSprite(model=model or "anthropic/claude-3-5-sonnet-20241022")
    return sprite.completion_text(prompt, system_prompt)


def llm_completion_json(prompt: str, system_prompt: str = None, schema: dict = None, model: str = None) -> dict:
    """JSON completion for CodeAgent"""
    sprite = LLMSmartSprite(model=model or "anthropic/claude-3-5-sonnet-20241022")
    return sprite.completion_json(prompt, system_prompt, schema)


def llm_analyze_code(code: str, language: str = "python", model: str = None) -> dict:
    """Code analysis for CodeAgent"""
    sprite = LLMSmartSprite(model=model or "anthropic/claude-3-5-sonnet-20241022")
    return sprite.analyze_code(code, language)


def llm_generate_code(description: str, language: str = "python", style: str = "clean", model: str = None) -> dict:
    """Code generation for CodeAgent"""
    sprite = LLMSmartSprite(model=model or "anthropic/claude-3-5-sonnet-20241022")
    return sprite.generate_code(description, language, style)


def llm_explain_concept(concept: str, level: str = "intermediate", model: str = None) -> str:
    """Concept explanation for CodeAgent"""
    sprite = LLMSmartSprite(model=model or "anthropic/claude-3-5-sonnet-20241022")
    return sprite.explain_concept(concept, level)


def llm_debug_error(error_message: str, code_context: str = "", model: str = None) -> dict:
    """Error debugging for CodeAgent"""
    sprite = LLMSmartSprite(model=model or "anthropic/claude-3-5-sonnet-20241022")
    return sprite.debug_error(error_message, code_context)


# Demo function
def demo_llm_sprite():
    """Demo the LLM sprite capabilities"""
    print("=== LLM Smart Sprite Demo ===")
    
    sprite = LLMSmartSprite()
    
    # Test text completion
    print("\n1. Text Completion:")
    response = sprite.completion_text("Explain what Python is in one sentence.")
    print(f"Response: {response}")
    
    # Test JSON completion
    print("\n2. JSON Completion:")
    response = sprite.completion_json(
        "List 3 benefits of using Python",
        schema={"type": "object", "properties": {"benefits": {"type": "array", "items": {"type": "string"}}}}
    )
    print(f"Response: {response}")
    
    # Test code analysis
    print("\n3. Code Analysis:")
    code = "def add(a, b): return a + b"
    response = sprite.analyze_code(code)
    print(f"Analysis: {response}")


if __name__ == "__main__":
    demo_llm_sprite()
