# Sprite MCP Server Architecture

🏗️ **Modular MCP Server Architecture with HTTP Streaming Protocol**

## Overview

The Sprite MCP Server has been refactored into a clean, modular architecture supporting different capability levels and modern HTTP streaming protocol.

## 📁 File Structure

```
gaia_sprite_mcp/
├── sprite_mcp_server_base.py      # Base class with common functionality
├── sprite_mcp_server_level1.py    # Level 1: Basic sprites only (11 tools)
├── sprite_mcp_server_level2.py    # Level 2: Basic sprites + CodeAgent (12 tools)
├── sprite_mcp_server.py           # Main entry point (defaults to Level 1)
├── http_client.py                 # HTTP client for testing
├── old/                           # Legacy files
│   └── sprite_mcp_server.py       # Original server (reference only)
└── README.md                      # Updated documentation
```

## 🏗️ Architecture Design

### Base Class (`SpriteMCPServerBase`)
- **Abstract base class** with common functionality
- **HTTP + Stdio support** - Dual transport protocols
- **FastAPI integration** - Modern web framework
- **Tool registration** - Automatic sprite tool setup
- **Error handling** - Graceful fallbacks

### Level 1 Server (`SpriteMCPServerLevel1`)
- **11 Basic Sprite Tools** - All core Gaia sprites
- **Lightweight** - Minimal dependencies
- **Production ready** - Stable and tested

### Level 2 Server (`SpriteMCPServerLevel2`)
- **11 Basic Sprite Tools** - Same as Level 1
- **+ AI CodeAgent Tool** - Advanced algorithm generation
- **Enhanced capabilities** - Complex multi-step workflows
- **Backward compatible** - Can be used anywhere Level 1 is used

## 🔧 Tool Comparison

| Tool Category | Level 1 | Level 2 | Description |
|---------------|---------|---------|-------------|
| **Basic Sprites** | ✅ 11 tools | ✅ 11 tools | Core Gaia sprites |
| **AI CodeAgent** | ❌ | ✅ 1 tool | Algorithm generation |
| **Total Tools** | **11** | **12** | |

### Basic Sprite Tools (Both Levels)
1. `agsearch` - AgFunder company database search
2. `market_research_tool` - TAM/CAGR market data
3. `web_search_tool` - Multi-source web search
4. `news_search_tool` - Specialized news search
5. `omni_search_tool` - Internal omni database
6. `wiki_search_tool` - Wikipedia search
7. `multimodel_query_tool` - Multi-LLM queries
8. `frame_search_tool` - Data frame search
9. `frame_list_tool` - List data frames
10. `investor_search_tool` - Investor search
11. `investor_stats_tool` - Investor analytics

### Level 2 Additional Tool
12. `sprite_codeagent_tool` - AI agent that writes Python algorithms using all sprites

## 🚀 Usage Examples

### Quick Start
```bash
# Default (Level 1)
python3 gaia_sprite_mcp/sprite_mcp_server.py

# Level 1 explicit
python3 gaia_sprite_mcp/sprite_mcp_server_level1.py

# Level 2 with CodeAgent
python3 gaia_sprite_mcp/sprite_mcp_server_level2.py
```

### HTTP Streaming (Production)
```bash
# Level 1 HTTP server
python3 gaia_sprite_mcp/sprite_mcp_server_level1.py --transport http --port 8000

# Level 2 HTTP server
python3 gaia_sprite_mcp/sprite_mcp_server_level2.py --transport http --port 8001

# Custom configuration
python3 gaia_sprite_mcp/sprite_mcp_server_level2.py --host 0.0.0.0 --port 8080
```

### Stdio Protocol (MCP Clients)
```bash
python3 gaia_sprite_mcp/sprite_mcp_server_level1.py --transport stdio
python3 gaia_sprite_mcp/sprite_mcp_server_level2.py --transport stdio
```

## 🔗 Integration Patterns

### CodeAgent Integration
- **Uses Level 1 internally** - Avoids circular dependencies
- **MCPAdapt + SmolAgentsAdapter** - Proper MCP integration
- **Automatic server management** - Starts/stops Level 1 server as needed

### HTTP API Endpoints
- `GET /` - Server information
- `GET /tools` - List available tools
- `POST /tools/{tool_name}` - Call specific tool
- `GET /health` - Health check

### Client Libraries
- **HTTP Client** - `http_client.py` for direct HTTP access
- **MCPAdapt** - For MCP-compatible clients
- **Direct REST** - Any HTTP client can connect

## 🎯 Benefits

### Modularity
- **Clean separation** - Base class + specific levels
- **Interchangeable** - Use Level 1 or Level 2 as needed
- **Extensible** - Easy to add Level 3, Level 4, etc.

### Production Ready
- **HTTP streaming** - Modern web protocol
- **FastAPI + Uvicorn** - High-performance ASGI stack
- **CORS support** - Web application integration
- **Health monitoring** - Built-in health checks

### Developer Experience
- **Simple usage** - One command to start
- **Clear documentation** - Comprehensive guides
- **Testing included** - Automated test suite
- **Error handling** - Graceful degradation

## 🧪 Testing

Run the comprehensive test suite:
```bash
python3 test_server_levels.py
```

Tests verify:
- ✅ Level 1 server (11 tools)
- ✅ Level 2 server (12 tools)
- ✅ HTTP streaming protocol
- ✅ CodeAgent integration
- ✅ Tool availability
- ✅ Health endpoints

## 🔄 Migration Guide

### From Old Server
The old `sprite_mcp_server.py` has been moved to `old/` directory and replaced with the new modular architecture.

**Before:**
```bash
python3 gaia_sprite_mcp/sprite_mcp_server.py
```

**After:**
```bash
# Same functionality (Level 1)
python3 gaia_sprite_mcp/sprite_mcp_server.py

# Or explicit Level 1
python3 gaia_sprite_mcp/sprite_mcp_server_level1.py

# Enhanced with CodeAgent (Level 2)
python3 gaia_sprite_mcp/sprite_mcp_server_level2.py
```

### CodeAgent Integration
The `sprite_codeagent.py` now uses Level 1 server internally to avoid circular dependencies while Level 2 exposes CodeAgent as an MCP tool.

## 🎉 Summary

The new modular architecture provides:

- **🏗️ Clean Design** - Base class + level-specific implementations
- **🔄 Interchangeable** - Level 1 and Level 2 can be used anywhere
- **🌐 HTTP Streaming** - Modern production-ready protocol
- **🤖 AI Integration** - CodeAgent available as MCP tool in Level 2
- **📊 Comprehensive Testing** - Automated verification
- **📚 Clear Documentation** - Easy to understand and use

The architecture is designed for scalability, maintainability, and ease of use while providing powerful capabilities for both basic sprite access and advanced AI-driven workflows.
