# AUG Contract

## Sprite Scripts

Every sprite script starts with spr_

Every sprite script can be tested on its own by running the script itself.

## Sprite Test Suite

All tools in sprites in the main folder, must be tested by running sprite_test.py
(Ignore the ones in draft/ subfolder)

Inspect the list of sprites in this folder, and ensure they are all tested by running sprite_test.py.  If not, correct it now.

This contract can be re-executed anytime by running:
EXECUTE AUG_CONTRACT.md

## Sprite MCP access

sprite_mcp_server.py implements a python FastMCP server per https://github.com/modelcontextprotocol/python-sdk

## Sprite fails

When a sprite run fails, details are always written to separate text log file eg: fail/spr_(sprite_name)/20250603-013252__out_of_memory.log
