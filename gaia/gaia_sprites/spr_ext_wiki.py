# Simple 1-line interface for Wikipedia

def wiki_search(query="Apple Inc", sentences=3):
    """One-liner to search Wikipedia and get summary"""
    try:
        import wikipedia
        
        # Search for the topic
        search_results = wikipedia.search(query, results=5)
        if not search_results:
            return [], {"total_results": 0, "error": "No results found"}
        
        results = []
        for title in search_results:
            try:
                summary = wikipedia.summary(title, sentences=sentences)
                page_url = f"https://en.wikipedia.org/wiki/{title.replace(' ', '_')}"
                
                result = {
                    'title': title,
                    'summary': summary,
                    'url': page_url,
                    'type': 'wikipedia'
                }
                results.append(result)
            except wikipedia.exceptions.DisambiguationError as e:
                # Use the first option from disambiguation
                try:
                    summary = wikipedia.summary(e.options[0], sentences=sentences)
                    page_url = f"https://en.wikipedia.org/wiki/{e.options[0].replace(' ', '_')}"
                    result = {
                        'title': e.options[0],
                        'summary': summary,
                        'url': page_url,
                        'type': 'wikipedia'
                    }
                    results.append(result)
                except:
                    continue
            except:
                continue
        
        return results, {"total_results": len(results), "query": query}
        
    except Exception as e:
        print(f"Error in wiki_search: {e}")
        return [], {"total_results": 0, "error": str(e)}

def wiki_page(title="Apple Inc"):
    """Get full Wikipedia page content"""
    try:
        import wikipedia
        
        page = wikipedia.page(title)
        
        result = {
            'title': page.title,
            'summary': page.summary,
            'content': page.content,
            'url': page.url,
            'categories': page.categories,
            'links': page.links[:20],  # First 20 links
            'references': page.references[:10],  # First 10 references
            'type': 'wikipedia_page'
        }
        
        return result, {"success": True, "title": title}
        
    except wikipedia.exceptions.DisambiguationError as e:
        return {
            'error': 'Disambiguation needed',
            'options': e.options[:10],  # First 10 options
            'type': 'disambiguation'
        }, {"success": False, "error": "disambiguation"}
        
    except wikipedia.exceptions.PageError:
        return {
            'error': f'Page "{title}" not found',
            'type': 'not_found'
        }, {"success": False, "error": "not_found"}
        
    except Exception as e:
        return {
            'error': str(e),
            'type': 'error'
        }, {"success": False, "error": str(e)}

def wiki_summary(query="Apple Inc", sentences=2):
    """Get just the summary of a Wikipedia topic"""
    try:
        import wikipedia
        
        summary = wikipedia.summary(query, sentences=sentences)
        page_url = f"https://en.wikipedia.org/wiki/{query.replace(' ', '_')}"
        
        return {
            'query': query,
            'summary': summary,
            'url': page_url,
            'sentences': sentences
        }, {"success": True}
        
    except wikipedia.exceptions.DisambiguationError as e:
        # Use the first option
        try:
            summary = wikipedia.summary(e.options[0], sentences=sentences)
            page_url = f"https://en.wikipedia.org/wiki/{e.options[0].replace(' ', '_')}"
            return {
                'query': e.options[0],
                'summary': summary,
                'url': page_url,
                'sentences': sentences,
                'note': f'Redirected from "{query}"'
            }, {"success": True, "redirected": True}
        except:
            return {
                'error': 'Disambiguation needed',
                'options': e.options[:5]
            }, {"success": False, "error": "disambiguation"}
            
    except Exception as e:
        return {
            'error': str(e)
        }, {"success": False, "error": str(e)}

def wiki_company(company_name="Apple Inc"):
    """Search for company information on Wikipedia"""
    return wiki_search(f"{company_name} company", sentences=4)

def wiki_person(person_name="Steve Jobs"):
    """Search for person information on Wikipedia"""
    return wiki_search(f"{person_name}", sentences=3)

def wiki_technology(tech_term="Artificial Intelligence"):
    """Search for technology information on Wikipedia"""
    return wiki_search(f"{tech_term}", sentences=4)

def wiki_random():
    """Get a random Wikipedia article"""
    try:
        import wikipedia
        
        title = wikipedia.random()
        summary = wikipedia.summary(title, sentences=2)
        page_url = f"https://en.wikipedia.org/wiki/{title.replace(' ', '_')}"
        
        return {
            'title': title,
            'summary': summary,
            'url': page_url,
            'type': 'random'
        }, {"success": True}
        
    except Exception as e:
        return {
            'error': str(e)
        }, {"success": False, "error": str(e)}

def wiki_quick_facts(query="Apple Inc"):
    """Get quick facts about a topic"""
    try:
        result, stats = wiki_page(query)
        if not stats.get('success'):
            return result, stats
        
        # Extract key information
        quick_facts = {
            'title': result['title'],
            'summary': result['summary'][:300] + "...",
            'url': result['url'],
            'categories': result['categories'][:5],
            'related_topics': result['links'][:10]
        }
        
        return quick_facts, {"success": True, "type": "quick_facts"}
        
    except Exception as e:
        return {
            'error': str(e)
        }, {"success": False, "error": str(e)}

# Usage examples:
# results, stats = wiki_search("Artificial Intelligence")
# page, stats = wiki_page("Apple Inc")
# summary, stats = wiki_summary("Machine Learning", sentences=3)
# results, stats = wiki_company("Tesla")
# results, stats = wiki_person("Elon Musk")
# article, stats = wiki_random()
# facts, stats = wiki_quick_facts("Python programming")

if __name__ == "__main__":
    """Test the Wikipedia sprite when run directly"""
    print("Testing spr_wikipedia sprite...")
    
    # Test basic search
    try:
        results, stats = wiki_search("Apple Inc", sentences=2)
        print(f"✓ Basic search test passed - found {stats.get('total_results', 0)} results")
        if results:
            print(f"Sample result: {results[0].get('title', 'N/A')}")
    except Exception as e:
        print(f"✗ Basic search test failed: {e}")
    
    # Test summary
    try:
        summary, stats = wiki_summary("Python (programming language)", sentences=2)
        if stats.get('success'):
            print(f"✓ Summary test passed")
            print(f"Summary: {summary.get('summary', 'N/A')[:100]}...")
        else:
            print(f"✗ Summary test failed: {summary.get('error')}")
    except Exception as e:
        print(f"✗ Summary test failed: {e}")
    
    # Test company search
    try:
        results, stats = wiki_company("Tesla")
        print(f"✓ Company search test passed - found {stats.get('total_results', 0)} results")
    except Exception as e:
        print(f"✗ Company search test failed: {e}")
    
    # Test random article
    try:
        article, stats = wiki_random()
        if stats.get('success'):
            print(f"✓ Random article test passed - got: {article.get('title', 'N/A')}")
        else:
            print(f"✗ Random article test failed: {article.get('error')}")
    except Exception as e:
        print(f"✗ Random article test failed: {e}")
    
    print("spr_wikipedia test complete.")
